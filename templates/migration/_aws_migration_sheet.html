<div id="data1" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">
			<h3>
				Create move group sheet csv from VM Mapping sheet and AWS AMS export sheet
				<span class="text-orange"> for rehosting VMs</span>
			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'data1_req')"
						id="defaultOpen">Requirements</button>
					<div id="data1_req" class="tabcontent">
						<p>
							1. Create sources and <span class="text"
								style="color:black; font-weight: bold">export csv file</span> in <span
								class="text" style="color:black; font-weight: bold">AWS Application
								Migration Service</span><br>
							2. Require <span class="text" style="color:black; font-weight: bold">AWS VM
								mapping sheet</span> filled with <span class="text"
								style="color:black; font-weight: bold">move groups</span> assigned and
							Migration path of VM as <span class="text"
								style="color:black; font-weight: bold">Rehost</span>.
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'data1_steps')">Steps</button>
					<div id="data1_steps" class="tabcontent">

						<p>1. Create sources and <span class="text"
								style="color:black; font-weight: bold">AWS AMS export csv file</span> in AWS
							Application Migration Service<br>
							2. Copy <a
								href="https://docs.google.com/spreadsheets/d/1op3izmi_rR7hGIOkrPNIhO04EUnfa2zklE0P0Q2MohU/edit?usp=sharing"
								target="_blank"><u>AWS VM Mapping sheet</u></a><br>
							3. Fill it with data<br>
							4. Assign VMs based upon move groups<br>
							5. Provide migration path of VMs as <span class="text"
								style="color:black; font-weight: bold">Rehost</span> for migration
							candidates<br>
							6. Download the updated <span class="text"
								style="color:black; font-weight: bold">AWS VM mapping sheet</span> csv file
							Locally<br>
							7. In the form, fill <br>
							&nbsp; &nbsp; a) Movegroup name<br>
							&nbsp; &nbsp; b) Upload AWS AMS Export csv file<br>
							&nbsp; &nbsp; c) Upload AWS VM mapping sheet csv file <br>
							8. After clicking upload button, Movegroup group migration sheet csv will be
							downloaded locally.<br>
							9. This migration sheet csv file can be used to import movegroup configuration
							for multiple VMs in AWS Application Migration Service for migrating VMs with
							correct metadata.<br>
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'data1_out')">Output</button>
					<div id="data1_out" class="tabcontent">
						Sample output will be added soon...
						<!-- <a href="https://docs.google.com/spreadsheets/d/1Gwts6K_O0e7hJgxb59_bO2_encJFxVZm0nG8bjyKrmI/view?usp=drive_link" target="_blank">Sample Output</a> -->
						<div class="img-div">
							<!-- <img src="./static/assets/images/buildsheet.png" style="margin-left: 0%; margin-top: -8%" width="100%" height="80%"> -->
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'data1_vid')">Video</button>
					<div id="data1_vid" class="tabcontent">
						<!-- Coming Soon... -->
						<iframe src="https://drive.google.com/file/d/1fnbMKjvSnCbbQhqqJ0dOdj_js4qXu2-z/view" width="690" height="420" allow="autoplay; fullscreen;"></iframe>
					</div>
					<button class="tablinks" onclick="openCity(event, 'data1_wn')">What Next?</button>
					<div id="data1_wn" class="tabcontent">
						<p>
							Go to <span class="text" style="color: black; font-weight:bold">AWS AMS
								import</span> tab and upload the CSV file downloaded in this step.<br>
							Movegroups will be configured for migration according to data from csv file.

						</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px; float: left; width : 100%; height : 500px;">
						<form id="form-green" action="gta_m2vm" method="post" enctype="multipart/form-data"
							onsubmit="addUUID(this);  revealText(this, 'orange2','');">
							<div class="form-group"
								style="padding-right:0px;margin-top: 10px; margin-left: -20px">
								<span class="span-form-group">Movegroup Name</span>
								<input type="text" placeholder="Enter Movegroup Name"
									class="form-group-class" id="move-group" style="border-radius:5px;"
									name="move-group" pattern="[a-z0-9]+" required><br><br><br>
								<span class="span-form-group">AWS AMS Export</span><br>
								<input type="file" id="csv-file" name="csv-file" accept=".csv"
									required><br><br><br>
								<span class="span-form-group">AWS VM App Mapping</span><br>
								<input type="file" id="excel-file" name="excel-file" accept=".csv"
									required><br><br>
							</div>&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
							<button id="gtam2vml" type="submit" class="btn arrow-btn orange-btn"
								style="border-radius:5px;margin-left: -145px;margin-top: -22px;margin-bottom: 27px;">Upload</button>
						</form>
						<div id="text-block-container-orange2" style="filter:none"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>