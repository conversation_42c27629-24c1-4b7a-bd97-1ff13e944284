# GCP Load Balancer Information Extractor (Multi-Project Optimized)

A high-performance shell script to extract all load balancer related information from Google Cloud Platform projects. This script supports scanning across all accessible projects with parallel processing for optimal speed. Converted from PowerShell functions and optimized for large-scale environments.

## Features

### Multi-Project Support with Parallel Processing
- **Scan All Projects**: Automatically discover and scan all accessible GCP projects
- **Parallel Processing**: Process multiple projects simultaneously (configurable concurrency)
- **Smart Filtering**: Automatically skip projects without load balancers
- **Quick Scan Mode**: Extract only essential data for faster execution
- **Progress Tracking**: Real-time progress updates during multi-project scans

### Comprehensive Data Extraction
The script extracts information from all load balancer components:

- **Forwarding Rules** (Load Balancer Frontend)
- **Backend Services** (Application Load Balancer backends)
- **Backend Buckets** (Cloud Storage backends)
- **Health Checks** (Health monitoring configurations)
- **SSL Certificates** (TLS/SSL certificates)
- **URL Maps** (HTTP routing rules)
- **Target Pools** (Legacy Network Load Balancer backends)

### Internal vs External Load Balancer Detection
The script automatically identifies and categorizes load balancers:
- **EXTERNAL**: Internet-facing load balancers
- **EXTERNAL_MANAGED**: External Application Load Balancers
- **INTERNAL**: Internal TCP/UDP load balancers
- **INTERNAL_MANAGED**: Internal Application Load Balancers
- **INTERNAL_SELF_MANAGED**: Internal self-managed load balancers

### Multiple Output Formats
- **CSV**: Structured data for analysis in spreadsheets
- **JSON**: Raw API responses for programmatic processing
- **Table**: Human-readable summary output

## Prerequisites

1. **gcloud CLI** installed and authenticated
   ```bash
   gcloud auth login
   ```

2. **jq** installed for JSON processing
   ```bash
   # Ubuntu/Debian
   sudo apt-get install jq
   
   # macOS
   brew install jq
   
   # Cloud Shell (already installed)
   ```

3. **GCP Permissions**: Your account needs the following roles:
   - `Compute Network Viewer` (minimum)
   - `Viewer` (recommended)

4. **APIs Enabled**: Compute Engine API must be enabled in the target project

## Usage

### Basic Usage

#### Single Project
```bash
./gcp_lb_extractor.sh -p YOUR_PROJECT_ID
```

#### All Projects (Recommended)
```bash
# Scan all accessible projects with load balancers
./gcp_lb_extractor.sh -a

# Quick scan mode (faster, forwarding rules only)
./gcp_lb_extractor.sh -a -q

# High-performance scan with 10 parallel jobs
./gcp_lb_extractor.sh -a -j 10
```

### Advanced Usage
```bash
# Scan all projects with custom settings
./gcp_lb_extractor.sh -a -f json -j 8 -o /tmp/lb_data

# Quick scan of all projects (fastest option)
./gcp_lb_extractor.sh --all-projects --quick-scan --max-jobs 10

# Scan all projects including those without LBs
./gcp_lb_extractor.sh -a --no-filter

# Single project with full extraction
./gcp_lb_extractor.sh -p my-project -o ./reports -f csv
```

### Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `-p, --project` | GCP Project ID (optional if using -a) | - |
| `-a, --all-projects` | Scan all accessible projects | `false` |
| `-o, --output-dir` | Output directory | `./lb_output` |
| `-f, --format` | Output format: csv, json, table | `csv` |
| `-j, --max-jobs` | Maximum parallel jobs (1-20) | `5` |
| `-q, --quick-scan` | Quick scan mode (forwarding rules only) | `false` |
| `--no-filter` | Don't filter projects - scan all even without LBs | `false` |
| `-h, --help` | Show help message | - |

## Performance Optimizations

### Multi-Project Mode
- **Parallel Processing**: Processes multiple projects simultaneously
- **Smart Filtering**: Automatically detects and skips projects without load balancers
- **Background Jobs**: Runs component extraction in parallel within each project
- **Progress Tracking**: Shows real-time progress during execution

### Quick Scan Mode
- **Faster Execution**: Extracts only forwarding rules (most important data)
- **Reduced API Calls**: Minimizes API requests for faster completion
- **Ideal for Discovery**: Perfect for initial load balancer discovery across many projects

### Execution Time Examples
- **Single Project (Full)**: 30-60 seconds
- **Single Project (Quick)**: 5-15 seconds
- **100 Projects (Full, 5 parallel)**: 10-20 minutes
- **100 Projects (Quick, 10 parallel)**: 2-5 minutes

## Output Files

### Multi-Project Structure
```
lb_output/
├── project-1/
│   ├── lb_frontend_YYYYMMDD_HHMMSS.csv
│   ├── lb_backend_YYYYMMDD_HHMMSS.csv
│   └── summary.txt
├── project-2/
│   ├── lb_frontend_YYYYMMDD_HHMMSS.csv
│   └── summary.txt
└── consolidated_summary_YYYYMMDD_HHMMSS.txt
```

### Single Project Structure
The script generates timestamped files for each component:

### CSV Files (when format=csv)
- `lb_frontend_YYYYMMDD_HHMMSS.csv` - Forwarding Rules
- `lb_backend_YYYYMMDD_HHMMSS.csv` - Backend Services
- `lb_backend_buckets_YYYYMMDD_HHMMSS.csv` - Backend Buckets
- `health_checks_YYYYMMDD_HHMMSS.csv` - Health Checks
- `ssl_certificates_YYYYMMDD_HHMMSS.csv` - SSL Certificates
- `url_maps_YYYYMMDD_HHMMSS.csv` - URL Maps
- `target_pools_YYYYMMDD_HHMMSS.csv` - Target Pools

### JSON Files (when format=json)
- Raw API responses for each component type

### Summary Report
- `lb_summary_YYYYMMDD_HHMMSS.txt` - Overall summary with counts and types

## Sample Output

### Forwarding Rules (Frontend) CSV
```csv
ProjectID,Name,Target,Description,LoadBalancingScheme,Location,Network,Subnetwork,IPProtocol,IPAddress,Ports,PortRange,BackendService,CreationTimestamp,Labels
my-project,web-lb-frontend,web-lb-target-proxy,Web application load balancer,EXTERNAL_MANAGED,global,default,,TCP,**************,80;443,,web-backend-service,2023-10-01T10:30:00.000-07:00,env:prod ; team:web
```

### Backend Services CSV
```csv
ProjectID,Name,Description,CreationTimestamp,LoadBalancingScheme,Location,Port,PortName,Protocol,HealthChecks,BackendGroups,Logging,LogSampleRate,AffinityCookieTtlSec,ConnectionDrainingTime,EnableCDN,SecurityPolicy,SessionAffinity,Timeout
my-project,web-backend-service,Backend for web application,2023-10-01T10:25:00.000-07:00,EXTERNAL_MANAGED,global,80,http,HTTP,web-health-check,web-instance-group,true,0.1,0,300,false,,NONE,30
```

## Use Cases

### 1. Load Balancer Inventory
Get a complete inventory of all load balancers in your project:
```bash
./gcp_lb_extractor.sh -p my-project -f table
```

### 2. Security Audit
Export all load balancer configurations for security review:
```bash
./gcp_lb_extractor.sh -p my-project -f csv -o ./security_audit
```

### 3. Migration Planning
Extract configurations for migration or disaster recovery planning:
```bash
./gcp_lb_extractor.sh -p source-project -f json -o ./migration_data
```

### 4. Cost Analysis
Identify all load balancer components for cost optimization:
```bash
./gcp_lb_extractor.sh -p my-project -f csv
# Analyze the CSV files to identify unused or redundant components
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```
   Error: Cannot access project 'my-project'
   ```
   **Solution**: Ensure you have the required permissions and the project ID is correct.

2. **API Not Enabled**
   ```
   Warning: Failed to retrieve forwarding rules
   ```
   **Solution**: Enable the Compute Engine API in your project.

3. **gcloud Not Authenticated**
   ```
   Error: Not authenticated with gcloud
   ```
   **Solution**: Run `gcloud auth login` to authenticate.

4. **jq Not Found**
   ```
   Error: jq is not installed
   ```
   **Solution**: Install jq using your system's package manager.

### Debug Mode
For troubleshooting, you can modify the script to add debug output:
```bash
# Add this line after the shebang to enable debug mode
set -x
```

## Integration with Other Tools

### Import into Excel/Google Sheets
The CSV files can be directly imported into spreadsheet applications for analysis.

### Use with Python/R
```python
import pandas as pd
df = pd.read_csv('lb_frontend_20231001_103000.csv')
# Analyze load balancer configurations
```

### Combine with Other GCP Tools
```bash
# Extract LB data and combine with cost data
./gcp_lb_extractor.sh -p my-project
gcloud billing budgets list --billing-account=BILLING_ACCOUNT_ID
```

## Contributing

This script is based on the PowerShell functions from the GCP inventory tool. To add new features or fix issues:

1. Test changes in a safe environment
2. Ensure backward compatibility
3. Update documentation
4. Add appropriate error handling

## License

This script is part of the GCP inventory toolset and follows the same licensing terms.
