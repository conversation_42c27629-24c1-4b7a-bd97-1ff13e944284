# GCP Load Balancer Information Extractor

A comprehensive shell script to extract all load balancer related information from Google Cloud Platform projects. This script is converted from the PowerShell functions and can be run directly in GCP Cloud Shell or any environment with `gcloud` CLI.

## Features

### Comprehensive Data Extraction
The script extracts information from all load balancer components:

- **Forwarding Rules** (Load Balancer Frontend)
- **Backend Services** (Application Load Balancer backends)
- **Backend Buckets** (Cloud Storage backends)
- **Health Checks** (Health monitoring configurations)
- **SSL Certificates** (TLS/SSL certificates)
- **URL Maps** (HTTP routing rules)
- **Target Pools** (Legacy Network Load Balancer backends)

### Internal vs External Load Balancer Detection
The script automatically identifies and categorizes load balancers:
- **EXTERNAL**: Internet-facing load balancers
- **EXTERNAL_MANAGED**: External Application Load Balancers
- **INTERNAL**: Internal TCP/UDP load balancers
- **INTERNAL_MANAGED**: Internal Application Load Balancers
- **INTERNAL_SELF_MANAGED**: Internal self-managed load balancers

### Multiple Output Formats
- **CSV**: Structured data for analysis in spreadsheets
- **JSON**: Raw API responses for programmatic processing
- **Table**: Human-readable summary output

## Prerequisites

1. **gcloud CLI** installed and authenticated
   ```bash
   gcloud auth login
   ```

2. **jq** installed for JSON processing
   ```bash
   # Ubuntu/Debian
   sudo apt-get install jq
   
   # macOS
   brew install jq
   
   # Cloud Shell (already installed)
   ```

3. **GCP Permissions**: Your account needs the following roles:
   - `Compute Network Viewer` (minimum)
   - `Viewer` (recommended)

4. **APIs Enabled**: Compute Engine API must be enabled in the target project

## Usage

### Basic Usage
```bash
./gcp_lb_extractor.sh -p YOUR_PROJECT_ID
```

### Advanced Usage
```bash
# Specify output directory and format
./gcp_lb_extractor.sh -p my-project -o /tmp/lb_data -f json

# Generate table output for quick review
./gcp_lb_extractor.sh -p my-project -f table

# Full example with all options
./gcp_lb_extractor.sh --project my-gcp-project --output-dir ./lb_reports --format csv
```

### Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `-p, --project` | GCP Project ID (required) | - |
| `-o, --output-dir` | Output directory | `./lb_output` |
| `-f, --format` | Output format: csv, json, table | `csv` |
| `-h, --help` | Show help message | - |

## Output Files

The script generates timestamped files for each component:

### CSV Files (when format=csv)
- `lb_frontend_YYYYMMDD_HHMMSS.csv` - Forwarding Rules
- `lb_backend_YYYYMMDD_HHMMSS.csv` - Backend Services
- `lb_backend_buckets_YYYYMMDD_HHMMSS.csv` - Backend Buckets
- `health_checks_YYYYMMDD_HHMMSS.csv` - Health Checks
- `ssl_certificates_YYYYMMDD_HHMMSS.csv` - SSL Certificates
- `url_maps_YYYYMMDD_HHMMSS.csv` - URL Maps
- `target_pools_YYYYMMDD_HHMMSS.csv` - Target Pools

### JSON Files (when format=json)
- Raw API responses for each component type

### Summary Report
- `lb_summary_YYYYMMDD_HHMMSS.txt` - Overall summary with counts and types

## Sample Output

### Forwarding Rules (Frontend) CSV
```csv
ProjectID,Name,Target,Description,LoadBalancingScheme,Location,Network,Subnetwork,IPProtocol,IPAddress,Ports,PortRange,BackendService,CreationTimestamp,Labels
my-project,web-lb-frontend,web-lb-target-proxy,Web application load balancer,EXTERNAL_MANAGED,global,default,,TCP,**************,80;443,,web-backend-service,2023-10-01T10:30:00.000-07:00,env:prod ; team:web
```

### Backend Services CSV
```csv
ProjectID,Name,Description,CreationTimestamp,LoadBalancingScheme,Location,Port,PortName,Protocol,HealthChecks,BackendGroups,Logging,LogSampleRate,AffinityCookieTtlSec,ConnectionDrainingTime,EnableCDN,SecurityPolicy,SessionAffinity,Timeout
my-project,web-backend-service,Backend for web application,2023-10-01T10:25:00.000-07:00,EXTERNAL_MANAGED,global,80,http,HTTP,web-health-check,web-instance-group,true,0.1,0,300,false,,NONE,30
```

## Use Cases

### 1. Load Balancer Inventory
Get a complete inventory of all load balancers in your project:
```bash
./gcp_lb_extractor.sh -p my-project -f table
```

### 2. Security Audit
Export all load balancer configurations for security review:
```bash
./gcp_lb_extractor.sh -p my-project -f csv -o ./security_audit
```

### 3. Migration Planning
Extract configurations for migration or disaster recovery planning:
```bash
./gcp_lb_extractor.sh -p source-project -f json -o ./migration_data
```

### 4. Cost Analysis
Identify all load balancer components for cost optimization:
```bash
./gcp_lb_extractor.sh -p my-project -f csv
# Analyze the CSV files to identify unused or redundant components
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```
   Error: Cannot access project 'my-project'
   ```
   **Solution**: Ensure you have the required permissions and the project ID is correct.

2. **API Not Enabled**
   ```
   Warning: Failed to retrieve forwarding rules
   ```
   **Solution**: Enable the Compute Engine API in your project.

3. **gcloud Not Authenticated**
   ```
   Error: Not authenticated with gcloud
   ```
   **Solution**: Run `gcloud auth login` to authenticate.

4. **jq Not Found**
   ```
   Error: jq is not installed
   ```
   **Solution**: Install jq using your system's package manager.

### Debug Mode
For troubleshooting, you can modify the script to add debug output:
```bash
# Add this line after the shebang to enable debug mode
set -x
```

## Integration with Other Tools

### Import into Excel/Google Sheets
The CSV files can be directly imported into spreadsheet applications for analysis.

### Use with Python/R
```python
import pandas as pd
df = pd.read_csv('lb_frontend_20231001_103000.csv')
# Analyze load balancer configurations
```

### Combine with Other GCP Tools
```bash
# Extract LB data and combine with cost data
./gcp_lb_extractor.sh -p my-project
gcloud billing budgets list --billing-account=BILLING_ACCOUNT_ID
```

## Contributing

This script is based on the PowerShell functions from the GCP inventory tool. To add new features or fix issues:

1. Test changes in a safe environment
2. Ensure backward compatibility
3. Update documentation
4. Add appropriate error handling

## License

This script is part of the GCP inventory toolset and follows the same licensing terms.
