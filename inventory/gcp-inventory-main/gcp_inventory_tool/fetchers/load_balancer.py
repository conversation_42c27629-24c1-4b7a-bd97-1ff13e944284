# --- File: gcp_inventory_tool/fetchers/load_balancer.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class LoadBalancerFetcher(ServiceFetcher):
    """
    Comprehensive Load Balancer fetcher that aggregates information from all 
    load balancer components including forwarding rules, backend services, 
    URL maps, target pools, and health checks to provide a complete view 
    of load balancer configurations.
    """
    SERVICE_NAME = "load_balancer"  # Unique key for this service type

    def __init__(self):
        super().__init__()
        # Caches to avoid redundant API calls
        self._backend_services_cache: Dict[str, compute_v1.BackendService] = {}
        self._url_maps_cache: Dict[str, compute_v1.UrlMap] = {}
        self._target_pools_cache: Dict[str, compute_v1.TargetPool] = {}
        self._health_checks_cache: Dict[str, compute_v1.HealthCheck] = {}
        self._ssl_certificates_cache: Dict[str, compute_v1.SslCertificate] = {}

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches comprehensive load balancer information by correlating data from
        forwarding rules, backend services, URL maps, target pools, and health checks.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting comprehensive Load Balancer fetch...")
        inventory = []
        
        # Initialize clients
        fwd_rules_client = compute_v1.ForwardingRulesClient(credentials=credentials)
        backend_services_client = compute_v1.BackendServicesClient(credentials=credentials)
        url_maps_client = compute_v1.UrlMapsClient(credentials=credentials)
        target_pools_client = compute_v1.TargetPoolsClient(credentials=credentials)
        health_checks_client = compute_v1.HealthChecksClient(credentials=credentials)
        ssl_certs_client = compute_v1.SslCertificatesClient(credentials=credentials)

        try:
            # Clear caches for each project
            self._clear_caches()
            
            # Pre-populate caches with all related resources
            self._populate_caches(project_id, backend_services_client, url_maps_client, 
                                target_pools_client, health_checks_client, ssl_certs_client)

            # Get all forwarding rules (entry points for load balancers)
            agg_list = fwd_rules_client.aggregated_list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing forwarding rules across regions/global...")

            for scope, response in agg_list:
                if response.forwarding_rules:
                    scope_name = get_resource_name(scope)
                    is_global = "global" in scope.lower()
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(response.forwarding_rules)} forwarding rules in scope {scope_name}...")

                    for rule in response.forwarding_rules:
                        lb_info = self._build_comprehensive_lb_info(project_id, rule, scope_name, is_global)
                        if lb_info:
                            inventory.append(lb_info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied accessing Compute API. Ensure API is enabled and necessary roles granted. Details: {e}")
            return []
        except api_exceptions.NotFound as e:
            logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
            return []
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to fetch load balancer information: {e}", exc_info=True)
            return []
        finally:
            # Close all clients
            for client in [fwd_rules_client, backend_services_client, url_maps_client, 
                          target_pools_client, health_checks_client, ssl_certs_client]:
                try:
                    client.transport.close()
                except Exception:
                    pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Load Balancer fetch. Found {len(inventory)} load balancers.")
        return inventory

    def _clear_caches(self):
        """Clear all internal caches."""
        self._backend_services_cache.clear()
        self._url_maps_cache.clear()
        self._target_pools_cache.clear()
        self._health_checks_cache.clear()
        self._ssl_certificates_cache.clear()

    def _populate_caches(self, project_id: str, backend_services_client, url_maps_client, 
                        target_pools_client, health_checks_client, ssl_certs_client):
        """Pre-populate caches with all related resources to avoid repeated API calls."""
        try:
            # Cache backend services
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Caching backend services...")
            for scope, response in backend_services_client.aggregated_list(project=project_id):
                if response.backend_services:
                    for bs in response.backend_services:
                        self._backend_services_cache[bs.self_link] = bs

            # Cache URL maps
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Caching URL maps...")
            for scope, response in url_maps_client.aggregated_list(project=project_id):
                if response.url_maps:
                    for url_map in response.url_maps:
                        self._url_maps_cache[url_map.self_link] = url_map

            # Cache target pools
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Caching target pools...")
            for scope, response in target_pools_client.aggregated_list(project=project_id):
                if response.target_pools:
                    for pool in response.target_pools:
                        self._target_pools_cache[pool.self_link] = pool

            # Cache health checks
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Caching health checks...")
            for scope, response in health_checks_client.aggregated_list(project=project_id):
                if response.health_checks:
                    for hc in response.health_checks:
                        self._health_checks_cache[hc.self_link] = hc

            # Cache SSL certificates
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Caching SSL certificates...")
            for scope, response in ssl_certs_client.aggregated_list(project=project_id):
                if response.ssl_certificates:
                    for cert in response.ssl_certificates:
                        self._ssl_certificates_cache[cert.self_link] = cert

        except Exception as e:
            logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Error populating caches: {e}")

    def _build_comprehensive_lb_info(self, project_id: str, rule: compute_v1.ForwardingRule, 
                                   scope_name: str, is_global: bool) -> Optional[Dict[str, Any]]:
        """Build comprehensive load balancer information from forwarding rule and related components."""
        try:
            location = scope_name if scope_name else ("global" if is_global else "Unknown")
            
            # Determine load balancer type and internal/external classification
            lb_type, lb_category = self._determine_lb_type_and_category(rule)

            # Base load balancer information
            lb_info = {
                "ProjectID": project_id,
                "Name": rule.name,
                "Type": lb_type,
                "Category": lb_category,  # Internal or External
                "Description": rule.description,
                "LoadBalancingScheme": str(rule.load_balancing_scheme),
                "Location": location,
                "IPAddress": rule.ip_address,
                "IPProtocol": str(rule.ip_protocol),
                "Ports": list(rule.ports) if rule.ports else [],
                "PortRange": rule.port_range,
                "NetworkTier": str(rule.network_tier),
                "Network": get_resource_name(rule.network),
                "Subnetwork": get_resource_name(rule.subnetwork),
                "CreationTimestamp": rule.creation_timestamp,
                "Labels": dict(rule.labels) if rule.labels else {},
                "Id": str(rule.id),
                "service": self.SERVICE_NAME
            }

            # Add target-specific information
            if rule.target:
                target_info = self._get_target_info(rule.target)
                lb_info.update(target_info)

            # Add backend service information if applicable
            if rule.backend_service:
                backend_info = self._get_backend_service_info(rule.backend_service)
                lb_info.update(backend_info)

            return lb_info

        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Error building LB info for rule {rule.name}: {e}")
            return None

    def _determine_lb_type_and_category(self, rule: compute_v1.ForwardingRule) -> tuple[str, str]:
        """
        Determine the type and category (Internal/External) of load balancer based on the forwarding rule.

        Returns:
            tuple: (load_balancer_type, category)

        GCP Load Balancing Schemes:
        - EXTERNAL: External load balancer (internet-facing)
        - EXTERNAL_MANAGED: External Application Load Balancer (managed)
        - INTERNAL: Internal TCP/UDP load balancer (regional)
        - INTERNAL_MANAGED: Internal Application Load Balancer (regional)
        - INTERNAL_SELF_MANAGED: Internal self-managed load balancer
        """
        scheme = str(rule.load_balancing_scheme).upper()

        # Determine category (Internal vs External)
        if scheme.startswith('INTERNAL'):
            category = "Internal"
        elif scheme.startswith('EXTERNAL'):
            category = "External"
        else:
            category = "Unknown"

        # Determine specific load balancer type
        if rule.target:
            target_type = rule.target.split('/')[-2] if '/' in rule.target else ""
            if "targetHttpProxies" in target_type:
                lb_type = "HTTP Load Balancer"
            elif "targetHttpsProxies" in target_type:
                lb_type = "HTTPS Load Balancer"
            elif "targetSslProxies" in target_type:
                lb_type = "SSL Proxy Load Balancer"
            elif "targetTcpProxies" in target_type:
                lb_type = "TCP Proxy Load Balancer"
            elif "targetPools" in target_type:
                lb_type = "Network Load Balancer (Legacy)"
            elif "targetInstances" in target_type:
                lb_type = "Target Instance Load Balancer"
            else:
                lb_type = "Load Balancer"
        elif rule.backend_service:
            # Backend service load balancers - determine type based on scheme
            if scheme == "INTERNAL_MANAGED":
                lb_type = "Internal Application Load Balancer"
            elif scheme == "EXTERNAL_MANAGED":
                lb_type = "External Application Load Balancer"
            elif scheme == "INTERNAL":
                lb_type = "Internal Network Load Balancer"
            elif scheme == "EXTERNAL":
                lb_type = "External Network Load Balancer"
            else:
                lb_type = "Backend Service Load Balancer"
        else:
            lb_type = "Load Balancer"

        return lb_type, category

    def _get_target_info(self, target_url: str) -> Dict[str, Any]:
        """Extract information about the load balancer target."""
        target_info = {
            "Target": get_resource_name(target_url),
            "TargetType": self._extract_target_type(target_url)
        }
        
        # Add specific target information based on type
        if "targetPools" in target_url:
            pool_info = self._get_target_pool_info(target_url)
            target_info.update(pool_info)
        
        return target_info

    def _extract_target_type(self, target_url: str) -> str:
        """Extract the target type from the target URL."""
        if not target_url:
            return "Unknown"
        
        type_mapping = {
            "targetHttpProxies": "HTTP Proxy",
            "targetHttpsProxies": "HTTPS Proxy", 
            "targetSslProxies": "SSL Proxy",
            "targetTcpProxies": "TCP Proxy",
            "targetPools": "Target Pool",
            "targetInstances": "Target Instance"
        }
        
        for key, value in type_mapping.items():
            if key in target_url:
                return value
        
        return "Unknown"

    def _get_target_pool_info(self, pool_url: str) -> Dict[str, Any]:
        """Get detailed information about a target pool."""
        pool = self._target_pools_cache.get(pool_url)
        if not pool:
            return {"TargetPoolInstances": [], "TargetPoolHealthChecks": []}
        
        return {
            "TargetPoolInstances": [get_resource_name(instance) for instance in pool.instances] if pool.instances else [],
            "TargetPoolHealthChecks": [get_resource_name(hc) for hc in pool.health_checks] if pool.health_checks else [],
            "TargetPoolSessionAffinity": str(pool.session_affinity) if hasattr(pool, 'session_affinity') else None
        }

    def _get_backend_service_info(self, backend_service_url: str) -> Dict[str, Any]:
        """Get detailed information about a backend service."""
        bs = self._backend_services_cache.get(backend_service_url)
        if not bs:
            return {"BackendService": get_resource_name(backend_service_url)}
        
        backend_info = {
            "BackendService": bs.name,
            "BackendServiceProtocol": str(bs.protocol),
            "BackendServicePort": bs.port,
            "BackendServicePortName": bs.port_name,
            "BackendServiceTimeout": bs.timeout_sec,
            "BackendServiceSessionAffinity": str(bs.session_affinity),
            "BackendServiceEnableCDN": bs.enable_cdn,
            "BackendServiceHealthChecks": [get_resource_name(hc) for hc in bs.health_checks] if bs.health_checks else [],
            "BackendServiceSecurityPolicy": get_resource_name(bs.security_policy) if bs.security_policy else None,
            "Backends": []
        }
        
        # Add backend details
        if bs.backends:
            for backend in bs.backends:
                backend_detail = {
                    "Group": get_resource_name(backend.group),
                    "BalancingMode": str(backend.balancing_mode),
                    "CapacityScaler": backend.capacity_scaler,
                    "MaxRatePerInstance": backend.max_rate_per_instance,
                    "MaxConnectionsPerInstance": backend.max_connections_per_instance
                }
                backend_info["Backends"].append(backend_detail)
        
        return backend_info

    def _get_health_check_details(self, health_check_urls: List[str]) -> List[Dict[str, Any]]:
        """Get detailed information about health checks."""
        health_check_details = []

        for hc_url in health_check_urls:
            hc = self._health_checks_cache.get(hc_url)
            if hc:
                hc_detail = {
                    "Name": hc.name,
                    "Type": str(hc.type_),
                    "CheckIntervalSec": hc.check_interval_sec,
                    "TimeoutSec": hc.timeout_sec,
                    "HealthyThreshold": hc.healthy_threshold,
                    "UnhealthyThreshold": hc.unhealthy_threshold,
                    "Port": getattr(hc, 'port', None),
                    "RequestPath": getattr(hc, 'request_path', None) if hasattr(hc, 'http_health_check') else None,
                    "Host": getattr(hc, 'host', None) if hasattr(hc, 'http_health_check') else None
                }
                health_check_details.append(hc_detail)
            else:
                health_check_details.append({"Name": get_resource_name(hc_url), "Type": "Unknown"})

        return health_check_details

    def _get_ssl_certificate_info(self, cert_urls: List[str]) -> List[Dict[str, Any]]:
        """Get detailed information about SSL certificates."""
        cert_details = []

        for cert_url in cert_urls:
            cert = self._ssl_certificates_cache.get(cert_url)
            if cert:
                cert_detail = {
                    "Name": cert.name,
                    "Type": str(cert.type_) if hasattr(cert, 'type_') else "Unknown",
                    "CreationTimestamp": cert.creation_timestamp,
                    "ExpireTime": cert.expire_time if hasattr(cert, 'expire_time') else None,
                    "SubjectAlternativeNames": list(cert.subject_alternative_names) if cert.subject_alternative_names else [],
                    "Managed": hasattr(cert, 'managed') and cert.managed is not None
                }
                cert_details.append(cert_detail)
            else:
                cert_details.append({"Name": get_resource_name(cert_url), "Type": "Unknown"})

        return cert_details

    def _get_url_map_info(self, url_map_url: str) -> Dict[str, Any]:
        """Get detailed information about URL map."""
        url_map = self._url_maps_cache.get(url_map_url)
        if not url_map:
            return {"UrlMap": get_resource_name(url_map_url)}

        url_map_info = {
            "UrlMap": url_map.name,
            "UrlMapDescription": url_map.description,
            "DefaultService": get_resource_name(url_map.default_service),
            "HostRules": [],
            "PathMatchers": []
        }

        # Add host rules
        if url_map.host_rules:
            for host_rule in url_map.host_rules:
                host_rule_info = {
                    "Hosts": list(host_rule.hosts) if host_rule.hosts else [],
                    "PathMatcher": host_rule.path_matcher,
                    "Description": host_rule.description
                }
                url_map_info["HostRules"].append(host_rule_info)

        # Add path matchers
        if url_map.path_matchers:
            for path_matcher in url_map.path_matchers:
                path_matcher_info = {
                    "Name": path_matcher.name,
                    "DefaultService": get_resource_name(path_matcher.default_service),
                    "Description": path_matcher.description,
                    "PathRules": []
                }

                # Add path rules
                if path_matcher.path_rules:
                    for path_rule in path_matcher.path_rules:
                        path_rule_info = {
                            "Paths": list(path_rule.paths) if path_rule.paths else [],
                            "Service": get_resource_name(path_rule.service) if hasattr(path_rule, 'service') else None,
                            "BackendService": get_resource_name(path_rule.backend_service) if hasattr(path_rule, 'backend_service') else None
                        }
                        path_matcher_info["PathRules"].append(path_rule_info)

                url_map_info["PathMatchers"].append(path_matcher_info)

        return url_map_info
