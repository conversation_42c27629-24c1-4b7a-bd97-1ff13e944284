# Load Balancer Fetcher Documentation

## Overview

The Load Balancer Fetcher (`load_balancer`) is a comprehensive fetcher that aggregates information from all load balancer-related components in Google Cloud Platform to provide a complete view of load balancer configurations.

## Features

### Comprehensive Data Collection
The fetcher collects data from multiple GCP services to build a complete picture of each load balancer:

- **Forwarding Rules**: Entry points for load balancers
- **Backend Services**: Service configurations and backend pools
- **URL Maps**: HTTP(S) routing rules and path matching
- **Target Pools**: Legacy load balancer backend pools
- **Health Checks**: Health monitoring configurations
- **SSL Certificates**: TLS/SSL certificate information

### Load Balancer Types and Categories

The fetcher automatically identifies and categorizes different types of GCP load balancers with clear Internal/External classification:

#### External Load Balancers (Internet-facing)
1. **External Application Load Balancer** (EXTERNAL_MANAGED)
2. **HTTP Load Balancer** (EXTERNAL with HTTP proxy)
3. **HTTPS Load Balancer** (EXTERNAL with HTTPS proxy)
4. **SSL Proxy Load Balancer** (EXTERNAL)
5. **TCP Proxy Load Balancer** (EXTERNAL)
6. **External Network Load Balancer** (EXTERNAL)
7. **Network Load Balancer (Legacy)** (EXTERNAL with target pools)

#### Internal Load Balancers (Private network only)
1. **Internal Application Load Balancer** (INTERNAL_MANAGED)
2. **Internal Network Load Balancer** (INTERNAL)
3. **Internal Self-Managed Load Balancer** (INTERNAL_SELF_MANAGED)

#### Other Types
- **Target Instance Load Balancer** (Direct instance targeting)

### Load Balancing Scheme Detection

The fetcher uses the `LoadBalancingScheme` field from GCP forwarding rules to determine if a load balancer is internal or external:

- **EXTERNAL**: External load balancer (internet-facing)
- **EXTERNAL_MANAGED**: External Application Load Balancer
- **INTERNAL**: Internal TCP/UDP load balancer (regional)
- **INTERNAL_MANAGED**: Internal Application Load Balancer (regional)
- **INTERNAL_SELF_MANAGED**: Internal self-managed load balancer

### Performance Optimizations

- **Caching**: Pre-populates caches with all related resources to minimize API calls
- **Batch Processing**: Uses aggregated list APIs to fetch resources efficiently
- **Parallel Processing**: Leverages the framework's concurrent execution capabilities

## Configuration

### Service Name
```yaml
services:
  - "load_balancer"
```

### Timeout Configuration
The load balancer fetcher is configured with a 'slow' timeout profile due to the comprehensive nature of data collection:

```python
SERVICE_TIMEOUT_CONFIGS = {
    'load_balancer': 'slow',  # Load balancer comprehensive fetch (multiple API calls)
}
```

## Output Schema

### Basic Load Balancer Information
```json
{
  "ProjectID": "string",
  "Name": "string",
  "Type": "string",
  "Category": "Internal|External",
  "Description": "string",
  "LoadBalancingScheme": "EXTERNAL|EXTERNAL_MANAGED|INTERNAL|INTERNAL_MANAGED|INTERNAL_SELF_MANAGED",
  "Location": "string",
  "IPAddress": "string",
  "IPProtocol": "string",
  "Ports": ["array"],
  "PortRange": "string",
  "NetworkTier": "string",
  "Network": "string",
  "Subnetwork": "string",
  "CreationTimestamp": "string",
  "Labels": {},
  "Id": "string",
  "service": "load_balancer"
}
```

### Target Information
```json
{
  "Target": "string",
  "TargetType": "string",
  "TargetPoolInstances": ["array"],
  "TargetPoolHealthChecks": ["array"],
  "TargetPoolSessionAffinity": "string"
}
```

### Backend Service Information
```json
{
  "BackendService": "string",
  "BackendServiceProtocol": "string",
  "BackendServicePort": "number",
  "BackendServicePortName": "string",
  "BackendServiceTimeout": "number",
  "BackendServiceSessionAffinity": "string",
  "BackendServiceEnableCDN": "boolean",
  "BackendServiceHealthChecks": ["array"],
  "BackendServiceSecurityPolicy": "string",
  "Backends": [
    {
      "Group": "string",
      "BalancingMode": "string",
      "CapacityScaler": "number",
      "MaxRatePerInstance": "number",
      "MaxConnectionsPerInstance": "number"
    }
  ]
}
```

## Usage Examples

### Basic Configuration
```yaml
# config.yaml
projects:
  - "my-gcp-project"

services:
  - "load_balancer"

output:
  format: "excel"
  file: "load_balancer_inventory.xlsx"
```

### Running the Inventory
```bash
python -m gcp_inventory_tool.cli --config config.yaml
```

### Filtering Load Balancers
The comprehensive data allows for advanced filtering and analysis:

#### Filter by Internal vs External
```python
# Example: Filter only internal load balancers
internal_lbs = [lb for lb in inventory if lb.get('Category') == 'Internal']

# Example: Filter only external load balancers
external_lbs = [lb for lb in inventory if lb.get('Category') == 'External']
```

#### Filter by Load Balancing Scheme
```python
# Example: Find all Internal Application Load Balancers
internal_albs = [lb for lb in inventory if lb.get('LoadBalancingScheme') == 'INTERNAL_MANAGED']

# Example: Find all External Application Load Balancers
external_albs = [lb for lb in inventory if lb.get('LoadBalancingScheme') == 'EXTERNAL_MANAGED']
```

#### Other Filtering Options
- Filter by load balancer type
- Identify load balancers without health checks
- Find load balancers with specific backend configurations
- Analyze SSL certificate usage across load balancers
- Find load balancers in specific networks or subnets

## Implementation Details

### Architecture
The fetcher follows a multi-phase approach:

1. **Cache Population**: Pre-fetch all related resources
2. **Forwarding Rule Processing**: Use forwarding rules as entry points
3. **Data Correlation**: Link forwarding rules with backend services, URL maps, etc.
4. **Information Aggregation**: Build comprehensive load balancer profiles

### Error Handling
- Graceful handling of API permission errors
- Fallback mechanisms for missing or inaccessible resources
- Detailed logging for troubleshooting

### Resource Relationships
The fetcher understands and maps the relationships between:
- Forwarding Rules → Backend Services
- Backend Services → Health Checks
- Backend Services → Instance Groups
- URL Maps → Backend Services
- SSL Certificates → HTTPS Load Balancers

## Troubleshooting

### Common Issues

1. **Permission Errors**
   - Ensure the service account has `Compute Network Viewer` role
   - Verify Compute Engine API is enabled

2. **Timeout Issues**
   - Increase timeout configuration for large projects
   - Consider reducing concurrent workers

3. **Missing Data**
   - Check if all required APIs are enabled
   - Verify resource permissions in the target project

### Debug Logging
Enable debug logging to see detailed fetcher operations:

```yaml
logging:
  level: "DEBUG"
  log_to_file: true
  log_file: "load_balancer_debug.log"
```

## Performance Considerations

- **Large Projects**: May take several minutes for projects with many load balancers
- **API Quotas**: Respects GCP API rate limits with built-in retry logic
- **Memory Usage**: Caches resources in memory during execution
- **Network**: Makes multiple API calls per load balancer for comprehensive data

## Future Enhancements

Potential improvements for future versions:
- Support for Application Load Balancer (ALB) configurations
- Integration with Cloud CDN settings
- Traffic policy and routing rule analysis
- Cost analysis integration
- Security policy detailed analysis
