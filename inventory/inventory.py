import os, subprocess
from inventory.merge import main_func, consolidate_inventory
import sys
from inventory.formatting import main, add_target_name_id, add_target_columns_azure
from inventory.aws_inventory.aws import driver as aws_driver
from config import paths
import logging
from inventory.oci_inv.oci_inventory import config
from inventory.oci_inv.oci_inventory import inventory
from inventory.oci_inv.oci_inventory.driver import driver
from app.utils import send_event

InventoryConfig = config.InventoryConfig
InventoryCollector = inventory.InventoryCollector


logger = logging.getLogger(__name__)
pwd = os.path.dirname(__file__)



def sub_process(cmd):
    try:
        outp = subprocess.run(['/bin/bash', '-c', cmd], stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                              encoding='utf-8', universal_newlines=True)
        logger.debug(str(outp.stdout) + "\n" + str(outp.stderr))
    except Exception as e:
        logger.exception("Subprocess Error: " + str(e))



def Inventory(request, rootpath):
    caller = str(request.form['call'])
    total_cost = 0

    # ----------------------------------- AWS Inventory -------------------------------------
    if caller == "aws":

        arn = str(request.form['arn'])
        regions = str(request.form['regions'])
        arns = arn.replace("\n", "").replace(" ", "").split(",")
        regions = "*" if regions == "" else regions.strip()
        target = request.form.get("aws-target", "0") == "1"
        ai_summary_gcp = request.form.get("ai_summary", "off") == "on"
        ai_summary_aws = request.form.get("ai_summary_aws", "off") == "on"
        

        logger.info("Total Accounts: " + str(len(arns)))
        logger.info("Regions: " + str(regions))
        send_event('log', log=f"Total Accounts: {len(arns)}")

        files = [os.path.join(pwd, f"aws_inventory/output/{arn.split(':')[4]}.xlsx") for arn in arns]
        consolidated_file = os.path.join(pwd, "aws_inventory/output/AWS-Inventory-Consolidated.xlsx")
        [os.system(f"rm -rf {file}") for file in files]
        os.system(f"rm -rf {consolidated_file}")
        send_event('log', log=f"Collecting inventory for {len(arns)} accounts...")
        aws_driver(set(arns), regions, target, send_event=send_event)

        not_found = [file for file in files if not os.path.isfile(file)]
        files = [file for file in files if os.path.isfile(file)]
        [logger.info("Successfully generate inventory: " + file.split('/')[-1].split('.')[0]) for file in files]
        [logger.error("Failed to generate inventory: " + file.split('/')[-1].split('.')[0]) for file in not_found]

        if target:
            for file in files:
                try:
                    add_target_name_id(file)
                except Exception as e:
                    logger.error(f"Failed to add target columns for {file}: {e}")


        for file in files:
            try:
                main(file, 'aws', False)
            except Exception as e:
                logger.exception("Failed to generate summary: " + str(e))
        logger.info("Summary Generated.")

        try:
            if ai_summary_gcp:
                total_cost = main_func(files, consolidated_file, 'aws','gcp', ai_summary_gcp)
            else :
                total_cost = main_func(files, consolidated_file, 'aws','aws', ai_summary_aws)
        except Exception as e:
            total_cost = 0
            logger.exception("Failed to generate consolidated inventory file: " + str(e))
        logger.info("Consolidated Inventory Generated.")

        if os.path.isfile(consolidated_file):
            try:
                consolidate_inventory(consolidated_file, "aws")
            except Exception as e:
                logger.exception(f"Error during consolidation for AWS: {e}")
            msg = "AWS inventory file created and downloaded successfully."
            logger.debug("AWS inventory file created and downloaded successfully.")
            return consolidated_file, total_cost, msg, 3
        else:
            err1 = "Internal Error"
            logger.error("Internal Error: File not Generated")
            return "", 0, err1, 2

    # ------------------------------- Azure Inventory -----------------------------------
    elif caller == "azure":
        # err = azconfig(request, rootpath, "pwsh")
        subscriptions = str(request.form['subscriptions']).strip()
        subscriptions = subscriptions.replace("\n", "").replace(" ", "")
        tenant = str(request.form['tenant']).strip()
        app_id = str(request.form['app_id']).strip()
        key = str(request.form['key']).strip()
        ai_summary_gcp = request.form.get("ai_summary", "off") == "on"
        ai_summary_aws = request.form.get("ai_summary_aws", "off") == "on"
        target = request.form.get("azure-target", "0") == "1"
        azure_to_aws = request.form.get("azure-aws-target", "0") == "1"
        if subscriptions == "":
            subscriptions = "*"
        success = False
        
        # if err != "":
        #     return "", err, err, 1

        # inventory = os.path.join(rootpath, 'inventory', 'ari-main')
        fileloc = os.path.join('/', 'root', 'AzureResourceInventory')
        file_path = fileloc
        try:
            files = os.listdir(fileloc)
            for file in files:
                file_path = os.path.join(fileloc, file)
                if os.path.isfile(file_path):
                    os.remove(file_path)
        except:
            pass
        if subscriptions == "*":
            # cmd = os.path.join(inventory, 'AzureResourceInventory.ps1')
            cmd = f"Invoke-ARI -TenantID {tenant} -SkipDiagram -NoAutoUpdate -Lite  -AppID {app_id} -Secret {key} -IncludeTags"
        else:
            # cmd = os.path.join(inventory, 'AzureResourceInventory.ps1')+" -SubscriptionID " + subscriptions
            cmd = f"Invoke-ARI -TenantID {tenant} -SubscriptionID {subscriptions} -SkipDiagram -NoAutoUpdate -Lite -AppID {app_id} -Secret {key} -IncludeTags"
    
        # print(cmd)
        # outp = ""
        
        outp = subprocess.Popen(['pwsh', '-c', cmd], stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                              encoding='utf-8')
        logs = ""
        with open(os.path.join(rootpath, 'templates', 'log.html'), 'w') as f:
            for line in iter(outp.stdout.readline, ''):
                stripped_line = line.strip()
                if "Connect-AzAccount: " in stripped_line:
                    outp.kill()
                    send_event('log', log="Azure login failed. Please check your credentials.")
                    err = "Please Enter correct credentials."
                    return "", 0, err, 1
                if not any(keyword in stripped_line for keyword in ["WARNING:", "[31;1m", "[0m", "install"]) and stripped_line:
                    send_event('log', log=stripped_line)
                logs += (stripped_line+"\n")
                if stripped_line.startswith("Excel file saved at:"):
                    file_path = stripped_line.split("Excel file saved at: ")[1].strip()
                    success = True
                f.write(stripped_line+"\n")
            outp.wait()

        if os.path.isfile(file_path) and success:
            if target:
                add_target_columns_azure(file_path)
            if azure_to_aws:
                from inventory.formatting import add_columns_to_sheet
                from inventory.constants import azure_to_aws_mappings
                add_columns_to_sheet(file_path, azure_to_aws_mappings)
            try:
                if ai_summary_gcp:
                    total_cost = main(file_path, 'azure','gcp', ai_summary_gcp, azure_target=target, gcp_target=False, azure_to_aws_target=azure_to_aws)
                if ai_summary_aws:
                    total_cost = main(file_path, 'azure','aws', ai_summary_aws, azure_target=target, gcp_target=False, azure_to_aws_target=azure_to_aws)
            except Exception as e:
                total_cost = 0
                logger.exception("Failed to generate summary: " + str(e))
            try:
                consolidate_inventory(file_path, "azure")
            except Exception as e:
                logger.exception(f"Error during consolidation for Azure: {e}")
            msg = "Azure inventory file created and downloaded successfully."
            return file_path, total_cost, msg, 0
        else:
            err1 = "Internal Error"
            return "", 0, err1, 1


 # -------------------------------- OCI Inventory ------------------------------
    elif caller == "oci":
        config_file=request.files['oci_zip_file']
        profile="DEFAULT"
        region = str(request.form['region'])
        target = request.form.get("oci-target", "0") == "1"
        ai_summary_gcp = request.form.get("ai_summary", "off") == "on"
        ai_summary_aws = request.form.get("ai_summary_aws", "off") == "on"
        if config_file and config_file.filename.endswith('.zip'):
            zip_path = os.path.join(pwd, "oci_access.zip")
            config_file.save(zip_path)
        # print(zip_path)
        command =f"oci session import --profile {profile} --session-archive {zip_path} --force"
        # print("About to run subprocess")

        result = subprocess.run(['/bin/bash', '-c', command], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        # print(result.stdout)
        # print(result.stderr)
        print("STDOUT:", result.stdout if result.stdout else "No STDOUT")
        print("STDERR:", result.stderr if result.stderr else "No STDERR")
        oci_output_file = os.path.join(pwd, "oci_inv/output/oci-inventory.xlsx")
        if result.returncode != 0:
            logger.error(f"OCI CLI returned non-zero exit code: {result.returncode}")
            return "", 0, "OCI Inventory CLI execution failed", 2
        logger.info(f"OCI CLI Output: {result.stderr}")
        output_dir = os.path.dirname(oci_output_file)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"Created output directory: {output_dir}")
        print(f"Calling driver() with: {oci_output_file}, {profile}, {region}")
        logger.info(f"Calling driver() with: {oci_output_file}, {profile}, {region}")
       
       
        # profile=""
        try:
            # print("HI")
            driver(oci_output_file, profile, region, target)
            print("Driver executed successfully")
            logger.info("OCI Inventory driver executed successfully")       
            
        except subprocess.TimeoutExpired:
             msg = "OCI session import command timed out."
             logger.error(msg)
             return "", 0, msg, 2
        except Exception as e:
            logger.error(f"OCI Inventory driver execution failed: {str(e)}")
            return "", 0, "OCI Inventory collection failed", 2

        if os.path.isfile(oci_output_file):
            try:
                if ai_summary_gcp:
                    total_cost = main(oci_output_file, 'oci', 'gcp', ai_summary_gcp)
                if ai_summary_aws:
                    total_cost = main(oci_output_file, 'oci','aws', ai_summary_aws)
            except Exception as e:
                total_cost = 0
                logger.warning(f"OCI Inventory formatting failed: {str(e)}")
                return oci_output_file, 0, "OCI Inventory formatting failed", 2

            msg = "OCI inventory file created and downloaded successfully."
            logger.info(msg)
            return oci_output_file, total_cost, msg, 3
        else:
            err1 = "Internal Error: OCI inventory file not generated."
            logger.error(err1)
            return "", 0, err1, 2

     # -------------------------------- GCP Inventory ------------------------------
    # else:
    #     org_id = request.form.get('orgid', '').strip() or '*'
    #     project_ids = request.form.get('projids', '').strip() or '*'
    #     folder_ids = request.form.get('folderids', '').strip() or '*'
    #     region = request.form.get('redis-reg', '').strip() or 'asia-south1'
    #     ai_summary = request.form.get("ai_summary", "off") == "on"
    #     send_event('log', log="Collecting GCP inventory...")
        

    #     paths.cleaner()

    #     powershell_script_path = os.path.join(rootpath, 'inventory', 'gcp-lld-generate', 'main.ps1')
    #     command = [
    #                 'pwsh',
    #                 '-File', 
    #                 powershell_script_path,
    #                 org_id,
    #                 folder_ids,
    #                 project_ids,
    #                 region
    #             ]
    #     try:
    #         process = subprocess.Popen(
    #             command,
    #             stdout=subprocess.PIPE,
    #             stderr=subprocess.STDOUT,
    #             text=True,
    #             bufsize=1,
    #             universal_newlines=True
    #         )
    #         logs = ""
    #         for line in iter(process.stdout.readline, ''):
    #             if line.strip().startswith("STREAM:"):
    #                 send_event('log', log=line.strip().replace("STREAM: ", ""))
    #             logs += (line.strip()+"\n")
            
    #         paths.logger(logs)
    #         process.stdout.close()
    #         return_code = process.wait()

    #         if return_code:
    #             raise subprocess.CalledProcessError(return_code, command)

    #     except FileNotFoundError:
    #         logger.error("PowerShell executable not found. Ensure 'pwsh' is in the system's PATH.")
    #         return "", "PowerShell not found.", "Server configuration error.", 2
            
    #     except subprocess.CalledProcessError as e:
    #         # This block now catches script execution errors (e.g., non-zero exit code).
    #         error_message = f"PowerShell script failed with exit code {e.returncode}:\n{e.stderr}"
    #         logger.error(error_message)
    #         paths.logger(error_message) # Also log the error to your custom log file.
    #         return "", e.stderr, "Failed to execute inventory script.", 2

    #     except Exception as e:
    #         logger.error(f"An unexpected error occurred while running the subprocess: {e}")
    #         return "", str(e), "An unexpected internal error occurred.", 2

    #     excel_filename = paths.readline(paths.file_name)

    #     if not os.path.isfile(excel_filename):
    #         error_details = "Inventory script ran but the output Excel file was not found."
    #         logger.error(error_details)
    #         return "", error_details, "Internal Error: Output file missing.", 2

    #     try:
    #         send_event('log', log="Formatting the inventory file...")
    #         main(excel_filename, 'gcp', ai_summary)
    #         success_message = "GCP inventory file created and downloaded successfully."
    #         send_event('log', log=success_message)
    #         return excel_filename, "", success_message, 3

    #     except Exception as e:
    #         logger.error(f"Failed during post-processing of the Excel file: {e}")
    #         return excel_filename, str(e), "GCP inventory file created and downloaded successfully.", 3
    

    else: # This block is for GCP based on your form's 'caller' logic
                organid = str(request.form['orgid']).strip()
                projids_str = str(request.form['projids']).strip()
              
                folderids = str(request.form['folderids']).strip()
                reg = str(request.form['redis-reg']).strip()
                target = request.form.get("gcp-target", "0") == "1"
                ai_summary_gcp = request.form.get("ai_summary", "off") == "on"
                ai_summary_aws = request.form.get("ai_summary_aws", "off") == "on"

                logger.info(f"Target value: {target}")
                
                # try:
                #     target = int(target_raw)
                #     print(target)
                # except ValueError:
                #     logger.error(f"Invalid 'target' value received: {target_raw}. Defaulting to 0.")
                #     target = 0

                if organid == "":
                    organid = '*'
                if projids_str == "":
                    projids_str = "*"
                if folderids == "":
                    folderids = "*"
                if reg == "":
                    reg = "asia-south1"
                
                final_projects_cli = projids_str if projids_str != "*" else ""
        

                output_base_dir = os.path.join(pwd, "gcp_inventory_output")
                os.makedirs(output_base_dir, exist_ok=True)

                excel_output_name = os.path.join(output_base_dir, "gcp_inventory.xlsx")

                try:
                    paths.cleaner() 
                except Exception as e:
                    logger.error(f"Error during path cleanup: {e}")
                    # return "", "", f"Internal Error: Failed to clean paths. {e}", 2


                # Path to your Python CLI script (gcp_inventory_tool/cli.py)
                cli_script_path = os.path.join(
                                    rootpath,
                                    "inventory",
                                    "gcp-inventory-main",
                                    "gcp_inventory_tool",
                                    "cli.py"
                                )

                # --- MODIFIED LINE BELOW: NEW PATH FOR config.yaml ---
                # The base path for gcp-inventory-main is os.path.dirname(os.path.dirname(cli_script_path))
                # Then, you add 'examples' to get to the desired directory
                cli_config_path = os.path.join(os.path.dirname(os.path.dirname(cli_script_path)), "examples", "config.yaml")


                if not os.path.exists(cli_script_path):
                    logger.critical(f"Python CLI script not found at: {cli_script_path}")
                    return "", 0, f"Internal Error: GCP Inventory CLI script not found. Path: {cli_script_path}", 2
                
                if not os.path.exists(cli_config_path):
                    logger.critical(f"Python CLI config file not found at: {cli_config_path}")
                    return "", 0, f"Internal Error: GCP Inventory CLI config.yaml not found. Path: {cli_config_path}", 2

                # Construct the command for the Python CLI
                command_args = [
                    sys.executable,
                    cli_script_path,
                    '--config', cli_config_path, # <--- This is where the new path is passed
                    '--output-file', excel_output_name,
                    '--format', 'excel',
                    '--log-level', 'INFO', # Consider DEBUG for more verbose cli.py logging during troubleshooting
                    '--no-log-to-file',
                ]

                if final_projects_cli:
                    command_args.extend(['--projects', final_projects_cli])
                if organid != "*":
                    command_args.extend(['--organization-id', organid])
                if folderids != "*":
                    command_args.extend(['--folders', folderids])
                else:
                    logger.warning("No specific project IDs provided via form or resolved from defaults. The GCP Inventory CLI will rely on its config.yaml or may error if no projects are defined there.")
                
                logger.debug(f"Executing Python CLI command for GCP: {' '.join(command_args)}")
                logger.debug(f"GCP Form Params - orgid: '{organid}', projids: '{projids_str}', folderids: '{folderids}', reg: '{reg}'")


                try:
                    result = subprocess.Popen(
                        command_args,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.STDOUT,
                        text=True
                    )

                    logs = ""
                    f = open("templates/log.html", "w")
                    for line in iter(result.stdout.readline, ''):
                        if line.strip().startswith("Inventory progress:"):
                            send_event('log', log=line.strip().replace("Inventory progress: ", ""))
                        logs += (line.strip()+"\n")
                        f.write(line.strip()+"\n")
                    f.close()

                    return_code = result.returncode

                    logger.info(f"Python CLI for GCP exited with code: {return_code}")

                    # if return_code != 0 or return_code is not None:
                    #     error_message = f"GCP Inventory generation failed. CLI exited with code {return_code}."
                    #     logger.error(error_message)
                    #     return "", 0, error_message, 2
                except FileNotFoundError:
                    logger.critical(f"Execution Error: Python executable or CLI script not found. Command: {command_args[0]} Path: {cli_script_path}")
                    return "", 0, "Internal Error: Python environment issue or CLI script missing.", 2
                except Exception as e:
                    logger.exception(f"An unexpected error occurred while running the GCP CLI subprocess: {e}")
                    return "", 0, f"Internal Error: An unexpected issue occurred during GCP inventory generation: {e}", 2

                final_generated_file = excel_output_name

                if os.path.isfile(final_generated_file):
                    try:
                        if ai_summary_gcp:
                            total_cost = main(final_generated_file, 'gcp','gcp', ai_summary_gcp, gcp_target=target)
                        else:
                            total_cost = main(final_generated_file, 'gcp','aws', ai_summary_aws, gcp_target=target)
                        try:
                            consolidate_inventory(final_generated_file, "gcp")
                        except Exception as e:
                            logger.exception(f"Error during consolidation for GCP: {e}")
                        msg = "GCP inventory file created and downloaded successfully."
                        logger.info(msg)
                        return final_generated_file, total_cost, msg, 3
                    except Exception as e:
                        total_cost = 0
                        logger.exception(f"Error during final formatting (inventory.formatting.main) for GCP: {e}")
                        return final_generated_file, 0, f"GCP inventory file created and downloaded successfully.", 3
                else:
                    err_msg = f"GCP Inventory file not found at expected path: {final_generated_file}. " \
                            f"CLI output might indicate reasons. Return Code: {return_code}"
                    logger.error(err_msg)
                    return "", 0, err_msg, 2
