import pandas as pd
import os
import re
from os import path
import subprocess
import json
import openpyxl
from openpyxl.styles import <PERSON><PERSON><PERSON>ill
from openpyxl.styles import Alignment
from openpyxl.utils import get_column_letter
from inventory.aws_inventory.services.ec2 import ec2
from inventory.aws_inventory.utilities.awsconfig import assume_aws_role_with_google_id_token
from inventory.aws_inventory.aws import get_regions

def vm_list_func(s):
    # Split the string into lines
    lines = s.strip().split('\n') #here strip removes trailing and leading zeros. Then split, splits the string on new line.

    # Extract the names from each line, excluding the header line
    names = [line.split()[0] for line in lines[1:]] #here the line is split on whitespaces and the 0th is the name.

    # Filter out empty names and strip any whitespace
    A = [name.strip() for name in names if name.strip() != '']

    return A

def extractor(i,s):
    split_parts = s.split('/')
    # Get the last part after splitting
    res = split_parts[-i] #Machine Type
    return res


#FLASK_PART_START
#Configure

#FLASK_PART_START

def mov_validate(mgg, data, path, dir, pid):

    xls = pd.ExcelFile(path)
    oname = 'validate_'+mgg+'_'+pid+'.xlsx'
    result_xl = os.path.join(dir,oname)

    json_data = json.loads(data)
    instances_list = list()

    for instance in json_data:
        instances_list.append(instance['name'])

    print(instances_list)

    #Create a Data Frame
    mg = mgg
    data = pd.DataFrame({"VM Name":[],
                        "Status":[],
                        "Move Group":[],
                        "Treatment":[],
                        "Target Size":[],
                        "Count of Disks":[],
                        "Target Disk Type":[],
                        "Project":[],
                        "Region":[],
                        "Zone":[],
                        "VPC":[],
                        "Subnet":[],
                        "Internal IP":[],
                        "External IP":[],
                        "Network Tags":[],
                        "Service Account":[],
                        "Labels":[]})


    sheets = xls.sheet_names

    df = pd.read_excel(xls, sheets[0]) #Reading the VMs sheet in VMMapping

    df.fillna("", inplace=True)


    for index, row in df.iterrows():
        try:
            print(row['Movegroup'])
        except:
            return '',"Please check whether you have uploaded the proper excel file."
        if row['Movegroup'] == mg:
            MapName = row['Instance Name']

            if MapName.lower() in instances_list:

                name = row['Name'] #VM Name

                #Network Tags in Array Form
                nettags = []
                tags = row['Network Tag'].strip().split('\n')
                for tag in tags:
                    nettags.append(tag)
                nettags.sort()
                #End of Nettags


                #option for externalIP

                if(row['Need for External IP']=='Yes' and row['External IP']==""):
                    print('in if')
                    exipopt = 'ephemeral'
                elif(row['Need for External IP']=='Yes' and row['External IP']!=""):
                    print('in elif')
                    exipopt = row['External IP']
                else:
                    exipopt = 'No External IP'


                print(exipopt)
                i = instances_list.index(name)

                json_machine_type = extractor(1, json_data[i]['machineType'])
                json_project = extractor(5, json_data[i]['selfLink'])
                json_zone = extractor(3, json_data[i]['selfLink'])
                json_region = json_zone[:-2]
                json_vpc = extractor(1, json_data[i]['networkInterfaces'][0]['network'])
                json_subnet = extractor(1, json_data[i]['networkInterfaces'][0]['subnetwork'])

                json_disktype = json_data[i]['disks'][0]['type'].lower()

                if(json_disktype == 'persistent'):
                    json_disktype = 'balanced'



                try:
                    json_nettags = json_data[i]['tags']['items']
                    json_nettags.sort()
                except KeyError:
                    json_nettags = 'No Net Tag'

                try:
                    json_labels = json_data[i]['labels']
                except KeyError:
                    json_labels = 'No Labels'

                status = 'Present('+json_data[i]['status']+')' #Status

                movegroup = f"{row['Movegroup']} (OK)" #Movegroup
                treatment = f"{row['Migration Path']} (OK)" #MigrationPath

                #TargetSize
                if(row['Target Size']==json_machine_type):
                    target_size = 'OK'
                else:
                    target_size = 'NOT OK('+json_machine_type+', should be '+row['Target Size']+')'

                #TargetDiskType
                if(row['Target Disk Type'].lower()==json_disktype): #Checks the disk type of bootdisk only
                    target_disk_type = 'OK'
                else:
                    print(row['Target Disk Type'].lower())
                    target_disk_type = 'NOT OK('+json_disktype+', should be '+row['Target Disk Type'].lower()+')'

                #DiskCount
                if(row['Disk Count']==len(json_data[i]['disks'])):
                    count_of_disks = 'OK'
                else:
                    count_of_disks = 'NOT OK('+str(len(json_data[i]['disks']))+', should be '+str(int(row['Disk Count']))+')'

                #Project
                if(extractor(1, row['Project']) == json_project):
                    project = 'OK'
                else:
                    project = 'NOT OK('+str(json_project)+', should be '+str(extractor(1, row['Project']))+')'

                #Region
                if(row['Region']==json_region):
                    region = 'OK'
                else:
                    region = 'NOT OK('+json_region+', should be '+row['Region']+')'

                #Zone
                if(row['Zone'] == json_zone):
                    zone = 'OK'
                else:
                    zone = 'NOT OK('+json_zone+', should be '+row['Zone']+')'

                #VPC
                if(row['VPC'] == json_vpc):
                    vpc = 'OK'
                else:
                    vpc = 'NOT OK('+json_vpc+', should be '+row['VPC']+')'

                #Subnet
                if(extractor(1, row['Subnet']) == json_subnet):
                    subnet = 'OK'
                else:
                    subnet = 'NOT OK('+json_subnet+', should be '+extractor(1, row['Subnet'])+')'

                #InternalIP

                if(row['Target Internal IP'] == json_data[i]['networkInterfaces'][0]['networkIP']):
                    internal_ip = 'OK'
                else:
                    internal_ip = 'NOT OK('+json_data[i]['networkInterfaces'][0]['networkIP']+', should be '+row['Target Internal IP']+')'

                #ExternalIP
                try:
                    if(row['Need for External IP']=='Yes' and type(json_data[i]['networkInterfaces'][0]['accessConfigs'][0]['natIP']) is str):
                        if(exipopt == 'ephemeral'):
                            external_ip = 'OK'
                        else:
                            if(json_data[i]['networkInterfaces'][0]['accessConfigs'][0]['natIP'] == exipopt):
                                external_ip = 'OK'
                            else:
                                external_ip = 'NOT OK('+json_data[i]['networkInterfaces'][0]['accessConfigs'][0]['natIP']+'should be '+exipopt+')'
                    else:
                        if(row['Need for External IP']=='No' and type(json_data[i]['networkInterfaces'][0]['accessConfigs'][0]['natIP']) is str):
                            external_ip = 'NOT OK (IP on Target Not Required)'
                        
                except KeyError:
                    if(row['Need for External IP']=='Yes'):
                        external_ip = 'NOT OK (Missing on the target)'
                    else:
                        external_ip = 'OK'

                if json_nettags == 'No Net Tag':
                    network_tags = 'NOT OK(Not Present)'
                else:
                    if(json_nettags == nettags):
                        network_tags = 'OK'
                    else:
                        network_tags = 'NOT OK( Tags should be '+str(nettags)+', but is '+str(json_nettags)+')'

                #Service Account
                if(json_data[i]['serviceAccounts'][0]['email'] == row['Service Account']):
                    service_account = 'OK'
                else:
                    service_account = 'NOT OK('+json_data[i]['serviceAccounts'][0]['email']+', should be '+row['Service Account']+')'


                label_lines = row['Labels'].split('\n')

                # Initialize an empty dictionary
                s_labels = {}

                # Iterate over the lines and split each line into key-value pairs
                for line in label_lines:
                    if line:
                        key, value = line.split(':', 1)  # Split each line into key-value pair
                        s_labels[key.strip()] = value.strip()



                if json_labels == 'No Labels':
                    labels = 'NOT OK(No Labels are Present)'
                else:
                    if(json_labels == s_labels):
                        labels = 'OK'
                    else:
                        #dict1-json_labels #dict2-s_labels
                        items_difference = {key: json_labels[key] for key in json_labels if key not in s_labels or json_labels[key] != s_labels[key]}
                        #TRue for present in VMMapping
                        #False for present in VM

                        if(items_difference.items() <= json_labels.items()):
                            labels = 'NOT OK( Additional Labels: '+str(items_difference)+' )'
                        else:
                            labels = 'NOT OK( Missing Labels: '+str(items_difference)+' )'




                print(name)
                print(status)
                print(movegroup)
                print(treatment)
                print(target_size)
                print(target_disk_type)
                print(count_of_disks)
                print(project)
                print(region)
                print(zone)
                print(vpc)
                print(subnet)
                print(internal_ip)
                print(external_ip)
                print(network_tags)
                print(service_account)
                print(labels)

                print('-------------------------------------------------------------')

            else:
                status = 'Not Present' #Status
                movegroup = row['Movegroup'] #Movegroup
                treatment = row['Migration Path'] #MigrationPath
                name = ''
                target_size = ''
                target_disk_type = ''
                count_of_disks = ''
                project = ''
                region = ''
                zone = ''
                vpc = ''
                subnet = ''
                internal_ip = ''
                external_ip = ''
                network_tags = ''
                service_account = ''
                labels = ''


                print(name)
                print(status)
                print(movegroup)
                print(treatment)
                print(target_size)
                print(target_disk_type)
                print(count_of_disks)
                print(project)
                print(region)
                print(zone)
                print(vpc)
                print(subnet)
                print(internal_ip)
                print(external_ip)
                print(network_tags)
                print(service_account)
                print(labels)


            temp = pd.DataFrame({"VM Name":[name],
                                "Status":[status],
                                "Move Group":[movegroup],
                                "Treatment":[treatment],
                                "Target Size":[target_size],
                                "Count of Disks":[count_of_disks],
                                "Target Disk Type":[target_disk_type],
                                "Project":[project],
                                "Region":[region],
                                "Zone":[zone],
                                "VPC":[vpc],
                                "Subnet":[subnet],
                                "Internal IP":[internal_ip],
                                "External IP":[external_ip],
                                "Network Tags":[network_tags],
                                "Service Account":[service_account],
                                "Labels":[labels]})

            data = pd.concat([data, temp])

    data

    data.to_excel(result_xl, index=False)

    #Formatting Part
    wb = openpyxl.load_workbook(result_xl)

    for sheet in wb.worksheets:

        val = sheet.max_column #should be replaced with max_col

        #Content for Heading Colour Filter
        for row in sheet.iter_rows(min_row=1, max_col=val, max_row=1):
            for c in row:
                c.fill = PatternFill('solid', fgColor = 'a9a9a9')

        #Dynamically edits the column width according to the content pasted there.
        dims = {}
        for row in sheet.rows:
            for cell in row:
                if cell.value:
                    dims[cell.column_letter] = max((dims.get(cell.column_letter, 0), len(str(cell.value))))
        for col, value in dims.items():
            sheet.column_dimensions[col].width = value+2

    # Set the column width as +2 greater than the first row's width
        for col, width in dims.items():
            sheet.column_dimensions[col].width = width + 6

        for row in sheet.rows:
                for cell in row:
                    if 'NOT OK' in str(cell.value):
                        cell.fill = PatternFill(start_color='FFC7CE', end_color='FFC7CE', fill_type='solid')  # Light Red
                    elif cell.value == 'OK' or '(OK)' in str(cell.value):
                        cell.fill = PatternFill(start_color='C6EFCE', end_color='C6EFCE', fill_type='solid')  # Light Green


                    if 'Present(' in str(cell.value):
                        cell.fill = PatternFill(start_color='C6EFCE', end_color='C6EFCE', fill_type='solid')  # Light Green
                    elif 'Not Present' in str(cell.value):
                        cell.fill = PatternFill(start_color='FFC7CE', end_color='FFC7CE', fill_type='solid')  # Light Red

        #Autofilter for all headings
        sheet.auto_filter.ref = sheet.dimensions


        #Remove ; and replace with new line
        for row in sheet.iter_rows():
            for cell in row:
                # Check if the cell value is a string
                if isinstance(cell.value, str):
                    # Use re.sub to replace the text in the cell
                    cell.value = re.sub(r' ; ', chr(13)+chr(10), cell.value)

    wb.save(result_xl)

    return result_xl, ''

def aws_mov_validate(mgg, ec2_data, path, dir, arn):
    """
    AWS-specific movegroup validation function
    Validates AWS VM mapping sheet against EC2 inventory data

    EC2 data structure (from inventory/aws_inventory/services/ec2.py):
    Index 0: Region, 1: Name, 2: Instance Id, 3: Instance Type, 4: CPU Cores, 5: Memory,
    Index 6: Availability Zone, 7: Private Ip, 8: Public Ip, 9: CreatedAt, 10: State,
    Index 11: Subnet Id, 12: Vpc Id, 13: Platform, 14: PlatformDetails, 15: Security Groups,
    Index 16: Volumes, 17: VolumeId, 18: size, 19: Total Size, 20: Type, 21: Iops, 22: Snapshot, 23: Tags
    """
    print(f"Starting AWS validation for movegroup: {mgg}")
    print(f"Excel file path: {path}")
    print(f"Output directory: {dir}")
    print(f"ARN: {arn}")

    try:
        xls = pd.ExcelFile(path)
        oname = 'validate_'+mgg+'_'+arn.split(':')[4]+'.xlsx'
        result_xl = os.path.join(dir, oname)
        print(f"Output file will be: {result_xl}")

        # Create instances list from EC2 data
        instances_list = []
        ec2_instances = ec2_data.get('EC2', [])
        print(f"EC2 data contains {len(ec2_instances)} rows (including header)")

        if len(ec2_instances) <= 1:
            print("Warning: No EC2 instances found in data")
            return '', "No EC2 instances found in the provided data"

        # Skip header row and extract instance names
        for i, instance_row in enumerate(ec2_instances[1:], 1):  # Skip header
            if len(instance_row) > 1:  # Ensure row has data
                instance_name = instance_row[1] if len(instance_row) > 1 else ""  # Name is at index 1
                print(f"Row {i}: Instance name = '{instance_name}' (row length: {len(instance_row)})")
                if instance_name and instance_name.strip():
                    instances_list.append(instance_name.lower().strip())

        print(f"AWS Instances found: {instances_list}")
       
        # Create DataFrame with AWS-specific columns
        mg = mgg
        
        sheets = xls.sheet_names
        df = pd.read_excel(xls, sheets[0])  # Reading the VMs sheet in VMMapping
        
        # Use all columns from the source excel file
        aws_columns = [
            "Name", "Status", "Movegroup", "Target Size", "Target Disk Type",
            "Region", "Subnet Id", "Security Group Id", "Target Internal IP",
            "IAM-Profile", "Need for External IP", "Tags"
        ]
        data = pd.DataFrame(columns=aws_columns)
        df.fillna("", inplace=True)

        validation_results = []
        processed_count = 0
        matched_count = 0

        for index, row in df.iterrows():
            try:
                movegroup_value = row.get('Movegroup', '')
                print(f"Processing row {index + 1}: Movegroup = '{movegroup_value}'")
            except Exception as e:
                print(f"Error reading row {index + 1}: {str(e)}")
                return '', f"Error reading Excel file at row {index + 1}. Please check the file format."

            if row.get('Movegroup', '') == mg:
                processed_count += 1
                MapName = row.get('Name', '').strip()
                print(f"  Checking VM: '{MapName}' in movegroup '{mg}'")

                if MapName and MapName.lower() in instances_list:
                    matched_count += 1
                    print(f"  ✓ Found matching instance: {MapName}")
                    # Find matching EC2 instance data
                    matching_instance = None
                    for instance_row in ec2_instances[1:]:  # Skip header
                        if (len(instance_row) > 1 and instance_row[1] and
                            instance_row[1].lower().strip() == MapName.lower().strip()):
                            matching_instance = instance_row
                            break

                    if matching_instance:
                        validated_row = row.to_dict()
                        # Fix: Use index 10 for State, not index 14 (PlatformDetails)
                        instance_state = matching_instance[10] if len(matching_instance) > 10 else "Unknown"
                        validated_row['Status'] = f'Present({instance_state})'
                        validated_row['Movegroup'] = f"{row['Movegroup']} (OK)"
                        validated_row['Migration Path'] = f"{row['Migration Path']} (OK)"

                        # Target Size validation
                        current_instance_type = matching_instance[3] if len(matching_instance) > 3 else ""
                        if row.get('Target Size') == current_instance_type:
                            validated_row['Target Size'] = f"{row['Target Size']} (OK)"
                        else:
                            validated_row['Target Size'] = f"NOT OK({current_instance_type}, should be {row.get('Target Size')})"

                        # Target Disk Type validation
                        current_disk_types = str(matching_instance[21]) if len(matching_instance) > 21 else ""
                        primary_disk_type = current_disk_types.split('\n')[0].strip() if current_disk_types else ""
                        if row.get('Target Disk Type', '').lower() == primary_disk_type.lower():
                            validated_row['Target Disk Type'] = f"{row['Target Disk Type']} (OK)"
                        else:
                            validated_row['Target Disk Type'] = f"NOT OK({primary_disk_type}, should be {row.get('Target Disk Type')})"

                        # Region validation
                        current_region = matching_instance[0] if len(matching_instance) > 0 else ""
                        if row.get('Region') == current_region:
                            validated_row['Region'] = f"{row['Region']} (OK)"
                        else:
                            validated_row['Region'] = f"NOT OK({current_region}, should be {row.get('Region')})"

                        # Subnet validation
                        current_subnet = matching_instance[11] if len(matching_instance) > 11 else ""
                        if row.get('Subnet Id') == current_subnet:
                            validated_row['Subnet Id'] = f"{row['Subnet Id']} (OK)"
                        else:
                            validated_row['Subnet Id'] = f"NOT OK({current_subnet}, should be {row.get('Subnet Id')})"

                        # Internal IP validation
                        current_internal_ip = matching_instance[7] if len(matching_instance) > 7 else ""
                        if str(row.get('Target Internal IP')) == current_internal_ip:
                            validated_row['Target Internal IP'] = f"{row['Target Internal IP']} (OK)"
                        else:
                            validated_row['Target Internal IP'] = f"NOT OK({current_internal_ip}, should be {row.get('Target Internal IP')})"
                        print(matching_instance)
                        # External IP validation
                        current_external_ip = matching_instance[8] if len(matching_instance) > 8 else ""
                        need_external_ip = row.get('Need for External IP', '')
                        if need_external_ip == 'Yes':
                            if current_external_ip:
                                validated_row['External IP'] = f"{current_external_ip} (OK)"
                            else:
                                validated_row['External IP'] = 'NOT OK (Missing on the target)'
                        else: # No
                            if current_external_ip:
                                validated_row['External IP'] = 'NOT OK (IP on Target Not Required)'
                            else:
                                validated_row['External IP'] = 'OK'
                        
                        # Security Groups
                        current_sg = matching_instance[16] if len(matching_instance) > 16 else ""
                        if row.get('Security Group Id') in current_sg:
                            validated_row['Security Group Id'] = f"{row['Security Group Id']} (OK)"
                        else:
                            validated_row['Security Group Id'] = f"NOT OK({current_sg}, should be {row.get('Security Group Id')})"

                        # Tags validation
                        sheet_tags_raw = row.get('Tags', '')
                        sheet_tags_dict = {}
                        if isinstance(sheet_tags_raw, str) and sheet_tags_raw:
                            for pair in re.split(r'[\n;]', sheet_tags_raw):
                                if ':' in pair:
                                    key, value = pair.split(':', 1)
                                    sheet_tags_dict[key.strip()] = value.strip()

                        # Parse inventory tags from the last column of matching_instance
                        inventory_tags_raw = matching_instance[-1] if matching_instance else ""
                        inventory_tags_dict = {}
                        if isinstance(inventory_tags_raw, str) and inventory_tags_raw:
                            for pair in inventory_tags_raw.split(';'):
                                if ':' in pair:
                                    key, value = pair.split(':', 1)
                                    inventory_tags_dict[key.strip()] = value.strip()

                        tags_summary = []
                        all_tags_ok = True

                        if not sheet_tags_dict:
                            validated_row['Tags'] = f"{row.get('Tags', '')} (OK)"
                        else:
                            for key, expected_value in sheet_tags_dict.items():
                                actual_value = inventory_tags_dict.get(key)
                                if actual_value is None:
                                    all_tags_ok = False
                                    tags_summary.append(f"Tag '{key}' not found")
                                elif actual_value != expected_value:
                                    all_tags_ok = False
                                    tags_summary.append(f"Tag '{key}' mismatch: expected '{expected_value}', found '{actual_value}'")

                            if all_tags_ok:
                                validated_row['Tags'] = f"{row.get('Tags', '')} (OK)"
                            else:
                                validated_row['Tags'] = "NOT OK: " + '; '.join(tags_summary)
                        
                        output_row = {key: validated_row.get(key) for key in aws_columns}
                        temp = pd.DataFrame([output_row])
                        data = pd.concat([data, temp], ignore_index=True)

                    else:
                        # Instance found in list but detailed data not available
                        validated_row = row.to_dict()
                        validated_row['Status'] = 'Present(Data Incomplete)'
                        output_row = {key: validated_row.get(key) for key in aws_columns}
                        temp = pd.DataFrame([output_row])
                        data = pd.concat([data, temp], ignore_index=True)

                else:
                    # Instance not found
                    print(f"  ✗ Instance not found in AWS: {MapName}")
                    validated_row = row.to_dict()
                    validated_row['Status'] = 'Not Present'
                    output_row = {key: validated_row.get(key) for key in aws_columns}
                    temp = pd.DataFrame([output_row])
                    data = pd.concat([data, temp], ignore_index=True)

        # Save to Excel with formatting (same as GCP version)
        data.to_excel(result_xl, index=False)

        # Apply formatting
        wb = openpyxl.load_workbook(result_xl)
        for sheet in wb.worksheets:
            val = sheet.max_column

            # Header formatting
            for row in sheet.iter_rows(min_row=1, max_col=val, max_row=1):
                for c in row:
                    c.fill = PatternFill('solid', fgColor='a9a9a9')

            # Auto-adjust column widths
            dims = {}
            for row in sheet.rows:
                for cell in row:
                    if cell.value:
                        dims[cell.column_letter] = max((dims.get(cell.column_letter, 0), len(str(cell.value))))
            for col, value in dims.items():
                sheet.column_dimensions[col].width = value + 6

            # Color coding for validation results
            for row in sheet.rows:
                for cell in row:
                    if 'NOT OK' in str(cell.value):
                        cell.fill = PatternFill(start_color='FFC7CE', end_color='FFC7CE', fill_type='solid')  # Light Red
                    elif cell.value == 'OK' or '(OK)' in str(cell.value):
                        cell.fill = PatternFill(start_color='C6EFCE', end_color='C6EFCE', fill_type='solid')  # Light Green
                    if 'Present(' in str(cell.value):
                        cell.fill = PatternFill(start_color='C6EFCE', end_color='C6EFCE', fill_type='solid')  # Light Green
                    elif 'Not Present' in str(cell.value):
                        cell.fill = PatternFill(start_color='FFC7CE', end_color='FFC7CE', fill_type='solid')  # Light Red

            # Auto filter
            sheet.auto_filter.ref = sheet.dimensions

            # Replace semicolons with newlines
            for row in sheet.iter_rows():
                for cell in row:
                    if isinstance(cell.value, str):
                        cell.value = re.sub(r' ; ', chr(13)+chr(10), cell.value)

            wb.save(result_xl)

            # Print validation summary
            print(f"\n=== AWS Movegroup Validation Summary ===")
            print(f"Movegroup: {mg}")
            print(f"Total VMs processed: {processed_count}")
            print(f"VMs found in AWS: {matched_count}")
            print(f"VMs not found in AWS: {processed_count - matched_count}")
            print(f"Validation file saved: {result_xl}")
            print(f"========================================\n")

            return result_xl, ''

    except Exception as e:
        print(f"Error in aws_mov_validate: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return '', f"AWS validation failed: {str(e)}"

def move_vali(request, rootpath):
    """
    GCP movegroup validation function
    """
    dir1 = os.path.join(rootpath, 'migration', 'move_val')

    # Get data from form and save it
    mg = request.form['move-group']
    pj = request.form['projid']
    file = request.files['excel-file']
    file.save(os.path.join(dir1, file.filename))
    excel_path = os.path.join(dir1, file.filename)

    config = subprocess.run(['/usr/bin/pwsh','-c','gcloud', 'config', 'set', 'project', pj],
                           stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)

    config_out = config.stderr
    print(config_out)

    if 'WARNING:' in config_out:
        lines = config_out.strip().split('.')
        msg = 'Check whether access is given to the mentioned service account.'
        err = lines[0]+'\n or Check whether access is given to the mentioned service account'
        return '', err, msg, 1

    gcloud_int = 'gcloud compute instances list --format=json'
    instances = subprocess.run(['/usr/bin/pwsh','-c', gcloud_int],
                              stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)

    data = instances.stdout

    # Call GCP validation function
    file_path, error = mov_validate(mg, data, excel_path, dir1, pj)

    if os.path.isfile(file_path):
        msg = "Validation file created and downloaded successfully."
        return file_path, "", msg, 0
    else:
        err1 = "Internal Error"
        return "", err1, error, 1

def aws_move_vali(request, rootpath):
    """
    AWS-specific movegroup validation function
    """
    dir1 = os.path.join(rootpath, 'migration', 'aws_mov_val')

    # Ensure directory exists
    os.makedirs(dir1, exist_ok=True)

    # Get data from form and save it
    mg = request.form['move-group']
    arn = request.form['arn']
    file = request.files['excel-file']
    file.save(os.path.join(dir1, file.filename))
    excel_path = os.path.join(dir1, file.filename)

    try:
        # Get AWS credentials using the ARN
        print(f"Attempting to authenticate with ARN: {arn}")
        creds, auth_error = assume_aws_role_with_google_id_token(arn)

        if auth_error:
            err = f"AWS authentication failed: {str(auth_error)}"
            print(f"Authentication error: {err}")
            return '', err, "Check AWS ARN and permissions", 1

        if not creds:
            err = "AWS authentication failed: No credentials returned"
            print(f"Credentials error: {err}")
            return '', err, "Check AWS ARN and permissions", 1

        # Validate credential structure
        required_keys = ['AccessKeyId', 'SecretAccessKey', 'SessionToken']
        missing_keys = [key for key in required_keys if key not in creds]
        if missing_keys:
            err = f"Invalid credential structure. Missing keys: {missing_keys}"
            print(f"Credential structure error: {err}")
            return '', err, "Invalid AWS credentials format", 1

        print(f"Successfully authenticated. Credential keys: {list(creds.keys())}")

        # Get all available AWS regions using the get_regions function
        region_list = get_regions(creds, "*")
        print(f"Retrieved {len(region_list)} regions from AWS: {region_list}")

        # Validate that we have regions to scan
        if not region_list:
            err = "No valid regions specified for EC2 inventory"
            print(f"Region error: {err}")
            return '', err, "No valid regions specified", 1

        # Get EC2 inventory data
        print("Calling EC2 service to get inventory data...")
        try:
            ec2_data = ec2(region_list, creds)
        except Exception as e:
            err = f"Failed to retrieve EC2 data: {str(e)}"
            print(f"EC2 service error: {err}")
            return '', err, "Failed to retrieve EC2 inventory", 1

        if not ec2_data or 'EC2' not in ec2_data:
            err = "Failed to retrieve EC2 data - no data returned"
            print(f"EC2 data error: {err}")
            return '', err, "Failed to retrieve EC2 inventory", 1

        ec2_instances_count = len(ec2_data.get('EC2', [])) - 1  # Subtract header row
        print(f"Retrieved EC2 data with {ec2_instances_count} instances")

        # Call AWS validation function
        print("Calling AWS validation function...")
        file_path, error = aws_mov_validate(mg, ec2_data, excel_path, dir1, arn)

        if error:
            print(f"Validation error: {error}")
            return '', error, "Validation failed", 1

        if os.path.isfile(file_path):
            msg = "AWS validation file created and downloaded successfully."
            print(f"Success: {msg}")
            return file_path, "", msg, 0
        else:
            err1 = "Internal Error: Validation file not created"
            print(f"File creation error: {err1}")
            return "", err1, error if error else "Unknown error", 1

    except Exception as e:
        import traceback
        err = f"AWS authentication or data retrieval failed: {str(e)}"
        print(f"Exception in aws_move_vali: {err}")
        print(f"Traceback: {traceback.format_exc()}")
        return '', err, "Check AWS ARN and permissions", 1
