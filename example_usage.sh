#!/bin/bash

# Example usage of the GCP Load Balancer Extractor
# This script demonstrates different ways to use the lb extractor

echo "GCP Load Balancer Extractor - Usage Examples"
echo "============================================="
echo ""

# Check if the extractor script exists
if [ ! -f "./gcp_lb_extractor.sh" ]; then
    echo "Error: gcp_lb_extractor.sh not found in current directory"
    exit 1
fi

# Make sure it's executable
chmod +x ./gcp_lb_extractor.sh

echo "Available usage examples:"
echo ""

echo "1. Basic usage (CSV output to ./lb_output):"
echo "   ./gcp_lb_extractor.sh -p YOUR_PROJECT_ID"
echo ""

echo "2. JSON output to custom directory:"
echo "   ./gcp_lb_extractor.sh -p YOUR_PROJECT_ID -o /tmp/lb_data -f json"
echo ""

echo "3. Table format for quick review:"
echo "   ./gcp_lb_extractor.sh -p YOUR_PROJECT_ID -f table"
echo ""

echo "4. Full example with all options:"
echo "   ./gcp_lb_extractor.sh --project YOUR_PROJECT_ID --output-dir ./reports --format csv"
echo ""

echo "5. Show help:"
echo "   ./gcp_lb_extractor.sh --help"
echo ""

# Interactive example
echo "Would you like to run an example? (y/n)"
read -r response

if [[ "$response" =~ ^[Yy]$ ]]; then
    echo ""
    echo "Enter your GCP Project ID:"
    read -r project_id
    
    if [ -z "$project_id" ]; then
        echo "No project ID provided. Exiting."
        exit 1
    fi
    
    echo ""
    echo "Choose output format:"
    echo "1) CSV (default)"
    echo "2) JSON"
    echo "3) Table"
    echo "Enter choice (1-3):"
    read -r format_choice
    
    case $format_choice in
        1|"")
            format="csv"
            ;;
        2)
            format="json"
            ;;
        3)
            format="table"
            ;;
        *)
            echo "Invalid choice. Using CSV format."
            format="csv"
            ;;
    esac
    
    echo ""
    echo "Running: ./gcp_lb_extractor.sh -p $project_id -f $format"
    echo ""
    
    # Run the extractor
    ./gcp_lb_extractor.sh -p "$project_id" -f "$format"
    
else
    echo "Example usage completed. Use the commands above to extract load balancer information."
fi

echo ""
echo "For more information, see README_LB_EXTRACTOR.md"
