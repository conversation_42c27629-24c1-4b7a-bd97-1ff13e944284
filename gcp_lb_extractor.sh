#!/bin/bash

# GCP Load Balancer Information Extractor
# This script extracts comprehensive load balancer information from GCP projects
# Converted from PowerShell functions.ps1 to bash for direct use in GCP Cloud Shell

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
PROJECT_ID=""
OUTPUT_DIR="./lb_output"
OUTPUT_FORMAT="csv"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
ALL_PROJECTS=false
MAX_PARALLEL_JOBS=5
QUICK_SCAN=false
FILTER_PROJECTS_WITH_LB=true

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Extract comprehensive GCP Load Balancer information including:
- Forwarding Rules (Frontend)
- Backend Services
- Backend Buckets
- Health Checks
- SSL Certificates
- URL Maps
- Target Pools

OPTIONS:
    -p, --project PROJECT_ID    GCP Project ID (optional if using --all-projects)
    -a, --all-projects          Scan all accessible projects
    -o, --output-dir DIR        Output directory (default: ./lb_output)
    -f, --format FORMAT         Output format: csv, json, table (default: csv)
    -j, --max-jobs NUM          Maximum parallel jobs (default: 5)
    -q, --quick-scan            Quick scan mode (forwarding rules only)
    --no-filter                 Don't filter projects - scan all even without LBs
    -h, --help                  Show this help message

EXAMPLES:
    $0 -p my-gcp-project
    $0 -a -f csv -j 10                    # Scan all projects with 10 parallel jobs
    $0 -a -q -o /tmp/lb_data              # Quick scan of all projects
    $0 -p my-project -o /tmp/lb_data -f json
    $0 --all-projects --quick-scan --max-jobs 8

REQUIREMENTS:
    - gcloud CLI installed and authenticated
    - jq installed (for JSON processing)
    - Appropriate GCP permissions (Compute Network Viewer role)

EOF
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        print_error "jq is not installed. Please install it first."
        print_info "Install with: sudo apt-get install jq (Ubuntu/Debian) or brew install jq (macOS)"
        exit 1
    fi
    
    # Check if authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
        print_error "Not authenticated with gcloud. Please run 'gcloud auth login' first."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to validate project access
validate_project() {
    local project_id=$1
    print_info "Validating access to project: $project_id"
    
    if ! gcloud projects describe "$project_id" &> /dev/null; then
        print_error "Cannot access project '$project_id'. Please check project ID and permissions."
        exit 1
    fi
    
    print_success "Project access validated"
}

# Function to create output directory
setup_output_dir() {
    local output_dir=$1
    print_info "Setting up output directory: $output_dir"
    
    mkdir -p "$output_dir"
    if [ ! -w "$output_dir" ]; then
        print_error "Cannot write to output directory: $output_dir"
        exit 1
    fi
    
    print_success "Output directory ready"
}

# Function to get all accessible projects
get_all_projects() {
    print_info "Discovering all accessible projects..."

    local projects_file="/tmp/gcp_projects_$$.json"

    if ! gcloud projects list --format=json > "$projects_file" 2>/dev/null; then
        print_error "Failed to list projects. Check permissions."
        rm -f "$projects_file"
        return 1
    fi

    local project_count=$(jq length "$projects_file")
    print_info "Found $project_count accessible projects"

    # Extract project IDs
    jq -r '.[].projectId' "$projects_file"
    rm -f "$projects_file"
}

# Function to quickly check if a project has load balancers
has_load_balancers() {
    local project_id=$1

    # Quick check - just count forwarding rules
    local count=$(gcloud compute forwarding-rules list --project="$project_id" --format="value(name)" 2>/dev/null | wc -l)

    if [ "$count" -gt 0 ]; then
        return 0  # Has load balancers
    else
        return 1  # No load balancers
    fi
}

# Function to filter projects with load balancers
filter_projects_with_lb() {
    local projects=("$@")
    local filtered_projects=()

    print_info "Filtering projects with load balancers..."

    for project in "${projects[@]}"; do
        if has_load_balancers "$project"; then
            filtered_projects+=("$project")
            print_info "✓ $project has load balancers"
        else
            print_info "✗ $project has no load balancers (skipping)"
        fi
    done

    printf '%s\n' "${filtered_projects[@]}"
}

# Function to extract load balancer data for a single project (optimized)
extract_project_lb_data() {
    local project_id=$1
    local output_dir=$2
    local format=$3
    local quick_scan=$4

    local project_dir="$output_dir/$project_id"
    mkdir -p "$project_dir"

    print_info "[$project_id] Starting extraction..."

    # Always extract forwarding rules (most important)
    extract_forwarding_rules "$project_id" "$project_dir" "$format" > /dev/null 2>&1

    if [ "$quick_scan" = "false" ]; then
        # Full extraction - run in parallel using background processes
        {
            extract_backend_services "$project_id" "$project_dir" "$format" > /dev/null 2>&1
        } &

        {
            extract_health_checks "$project_id" "$project_dir" "$format" > /dev/null 2>&1
        } &

        {
            extract_ssl_certificates "$project_id" "$project_dir" "$format" > /dev/null 2>&1
        } &

        {
            extract_url_maps "$project_id" "$project_dir" "$format" > /dev/null 2>&1
        } &

        {
            extract_target_pools "$project_id" "$project_dir" "$format" > /dev/null 2>&1
        } &

        {
            extract_backend_buckets "$project_id" "$project_dir" "$format" > /dev/null 2>&1
        } &

        # Wait for all background jobs to complete
        wait
    fi

    print_success "[$project_id] Extraction completed"

    # Generate project summary
    generate_project_summary "$project_id" "$project_dir"
}

# Function to generate summary for a single project
generate_project_summary() {
    local project_id=$1
    local project_dir=$2

    local summary_file="$project_dir/summary.txt"
    local fr_file="$project_dir/forwarding_rules_${TIMESTAMP}.json"

    cat > "$summary_file" << EOF
Project: $project_id
Generated: $(date)

Load Balancer Summary:
EOF

    if [ -f "$fr_file" ]; then
        local lb_count=$(jq length "$fr_file" 2>/dev/null || echo "0")
        echo "Total Load Balancers: $lb_count" >> "$summary_file"

        if [ "$lb_count" -gt 0 ]; then
            echo "" >> "$summary_file"
            echo "Load Balancer Types:" >> "$summary_file"
            jq -r '.[] | .loadBalancingScheme // "UNKNOWN"' "$fr_file" | sort | uniq -c | while read count scheme; do
                echo "- $scheme: $count" >> "$summary_file"
            done

            echo "" >> "$summary_file"
            echo "Load Balancers:" >> "$summary_file"
            jq -r '.[] | "- \(.name // "unnamed") (\(.loadBalancingScheme // "unknown")) - \(.IPAddress // "no-ip")"' "$fr_file" >> "$summary_file"
        fi
    else
        echo "Total Load Balancers: 0" >> "$summary_file"
    fi
}

# Function to extract forwarding rules (Load Balancer Frontend)
extract_forwarding_rules() {
    local project_id=$1
    local output_dir=$2
    local format=$3
    
    print_info "Extracting Forwarding Rules (LB Frontend) for project: $project_id"
    
    local json_file="$output_dir/forwarding_rules_${TIMESTAMP}.json"
    local csv_file="$output_dir/lb_frontend_${TIMESTAMP}.csv"
    
    # Get forwarding rules in JSON format
    if ! gcloud compute forwarding-rules list --project="$project_id" --format=json > "$json_file" 2>/dev/null; then
        print_warning "Failed to retrieve forwarding rules. API might not be enabled or no permissions."
        return 1
    fi
    
    local count=$(jq length "$json_file")
    print_info "Found $count forwarding rules"
    
    if [ "$count" -eq 0 ]; then
        print_warning "No forwarding rules found in project $project_id"
        return 0
    fi
    
    # Convert to CSV format
    if [ "$format" = "csv" ] || [ "$format" = "table" ]; then
        echo "ProjectID,Name,Target,Description,LoadBalancingScheme,Location,Network,Subnetwork,IPProtocol,IPAddress,Ports,PortRange,BackendService,CreationTimestamp,Labels" > "$csv_file"
        
        jq -r --arg project_id "$project_id" '
        .[] | 
        [
            $project_id,
            .name // "",
            (.target // "" | split("/")[-1]),
            (.description // "" | gsub(","; " ; ")),
            .loadBalancingScheme // "",
            (if .region then (.region | split("/")[-1]) else (.target // "" | split("/")[-3]) end),
            (.network // "" | split("/")[-1]),
            (.subnetwork // "" | split("/")[-1]),
            .IPProtocol // "",
            .IPAddress // "",
            (if .ports then (.ports | join(" ; ")) else "" end),
            .portRange // "",
            (.backendService // "" | split("/")[-1]),
            .creationTimestamp // "",
            (if .labels then (.labels | to_entries | map("\(.key):\(.value)") | join(" ; ")) else "" end)
        ] | @csv
        ' "$json_file" >> "$csv_file"
        
        print_success "Forwarding rules exported to: $csv_file"
    fi
    
    if [ "$format" = "json" ]; then
        print_success "Forwarding rules exported to: $json_file"
    fi
    
    if [ "$format" = "table" ]; then
        print_info "Forwarding Rules Summary:"
        echo "----------------------------------------"
        jq -r --arg project_id "$project_id" '
        .[] | 
        "Name: \(.name // "N/A")
Type: \(.loadBalancingScheme // "N/A") 
Location: \(if .region then (.region | split("/")[-1]) else (.target // "" | split("/")[-3]) end)
IP: \(.IPAddress // "N/A")
Target: \(.target // "" | split("/")[-1])
Backend: \(.backendService // "" | split("/")[-1])
----------------------------------------"
        ' "$json_file"
    fi
}

# Function to extract backend services
extract_backend_services() {
    local project_id=$1
    local output_dir=$2
    local format=$3

    print_info "Extracting Backend Services for project: $project_id"

    local json_file="$output_dir/backend_services_${TIMESTAMP}.json"
    local csv_file="$output_dir/lb_backend_${TIMESTAMP}.csv"

    # Get backend services in JSON format
    if ! gcloud compute backend-services list --project="$project_id" --format=json > "$json_file" 2>/dev/null; then
        print_warning "Failed to retrieve backend services. API might not be enabled or no permissions."
        return 1
    fi

    local count=$(jq length "$json_file")
    print_info "Found $count backend services"

    if [ "$count" -eq 0 ]; then
        print_warning "No backend services found in project $project_id"
        return 0
    fi

    # Convert to CSV format
    if [ "$format" = "csv" ] || [ "$format" = "table" ]; then
        echo "ProjectID,Name,Description,CreationTimestamp,LoadBalancingScheme,Location,Port,PortName,Protocol,HealthChecks,BackendGroups,Logging,LogSampleRate,AffinityCookieTtlSec,ConnectionDrainingTime,EnableCDN,SecurityPolicy,SessionAffinity,Timeout" > "$csv_file"

        jq -r --arg project_id "$project_id" '
        .[] |
        [
            $project_id,
            .name // "",
            (.description // "" | gsub(","; " ; ")),
            .creationTimestamp // "",
            .loadBalancingScheme // "",
            (.selfLink | split("/")[-3]),
            .port // "",
            .portName // "",
            .protocol // "",
            (if .healthChecks then (.healthChecks | map(split("/")[-1]) | join(" ; ")) else "" end),
            (if .backends then (.backends | map(.group | split("/")[-1]) | join(" ; ")) else "" end),
            (.logConfig.enable // ""),
            (.logConfig.sampleRate // ""),
            .affinityCookieTtlSec // "",
            (.connectionDraining.drainingTimeoutSec // ""),
            .enableCDN // "",
            (.securityPolicy // "" | split("/")[-1]),
            .sessionAffinity // "",
            .timeoutSec // ""
        ] | @csv
        ' "$json_file" >> "$csv_file"

        print_success "Backend services exported to: $csv_file"
    fi

    if [ "$format" = "json" ]; then
        print_success "Backend services exported to: $json_file"
    fi
}

# Function to extract backend buckets
extract_backend_buckets() {
    local project_id=$1
    local output_dir=$2
    local format=$3

    print_info "Extracting Backend Buckets for project: $project_id"

    local json_file="$output_dir/backend_buckets_${TIMESTAMP}.json"
    local csv_file="$output_dir/lb_backend_buckets_${TIMESTAMP}.csv"

    # Get backend buckets in JSON format
    if ! gcloud compute backend-buckets list --project="$project_id" --format=json > "$json_file" 2>/dev/null; then
        print_warning "Failed to retrieve backend buckets. API might not be enabled or no permissions."
        return 1
    fi

    local count=$(jq length "$json_file")
    print_info "Found $count backend buckets"

    if [ "$count" -eq 0 ]; then
        print_warning "No backend buckets found in project $project_id"
        return 0
    fi

    # Convert to CSV format
    if [ "$format" = "csv" ] || [ "$format" = "table" ]; then
        echo "ProjectID,Name,BucketName,Description,CreationTimestamp,Location,EnableCDN,CacheMode,ClientTtl,DefaultTtl,MaxTtl" > "$csv_file"

        jq -r --arg project_id "$project_id" '
        .[] |
        [
            $project_id,
            .name // "",
            .bucketName // "",
            (.description // "" | gsub(","; " ; ")),
            .creationTimestamp // "",
            (.selfLink | split("/")[-3]),
            .enableCDN // "",
            (.cdnPolicy.cacheMode // ""),
            (.cdnPolicy.clientTtl // ""),
            (.cdnPolicy.defaultTtl // ""),
            (.cdnPolicy.maxTtl // "")
        ] | @csv
        ' "$json_file" >> "$csv_file"

        print_success "Backend buckets exported to: $csv_file"
    fi

    if [ "$format" = "json" ]; then
        print_success "Backend buckets exported to: $json_file"
    fi
}

# Function to extract health checks
extract_health_checks() {
    local project_id=$1
    local output_dir=$2
    local format=$3

    print_info "Extracting Health Checks for project: $project_id"

    local json_file="$output_dir/health_checks_${TIMESTAMP}.json"
    local csv_file="$output_dir/health_checks_${TIMESTAMP}.csv"

    # Get health checks in JSON format
    if ! gcloud compute health-checks list --project="$project_id" --format=json > "$json_file" 2>/dev/null; then
        print_warning "Failed to retrieve health checks. API might not be enabled or no permissions."
        return 1
    fi

    local count=$(jq length "$json_file")
    print_info "Found $count health checks"

    if [ "$count" -eq 0 ]; then
        print_warning "No health checks found in project $project_id"
        return 0
    fi

    # Convert to CSV format
    if [ "$format" = "csv" ] || [ "$format" = "table" ]; then
        echo "ProjectID,Name,Type,Description,CheckIntervalSec,TimeoutSec,HealthyThreshold,UnhealthyThreshold,Port,RequestPath,Host" > "$csv_file"

        jq -r --arg project_id "$project_id" '
        .[] |
        [
            $project_id,
            .name // "",
            .type // "",
            (.description // "" | gsub(","; " ; ")),
            .checkIntervalSec // "",
            .timeoutSec // "",
            .healthyThreshold // "",
            .unhealthyThreshold // "",
            (if .httpHealthCheck then .httpHealthCheck.port elif .httpsHealthCheck then .httpsHealthCheck.port elif .tcpHealthCheck then .tcpHealthCheck.port else "" end),
            (if .httpHealthCheck then .httpHealthCheck.requestPath elif .httpsHealthCheck then .httpsHealthCheck.requestPath else "" end),
            (if .httpHealthCheck then .httpHealthCheck.host elif .httpsHealthCheck then .httpsHealthCheck.host else "" end)
        ] | @csv
        ' "$json_file" >> "$csv_file"

        print_success "Health checks exported to: $csv_file"
    fi

    if [ "$format" = "json" ]; then
        print_success "Health checks exported to: $json_file"
    fi
}

# Function to extract SSL certificates
extract_ssl_certificates() {
    local project_id=$1
    local output_dir=$2
    local format=$3

    print_info "Extracting SSL Certificates for project: $project_id"

    local json_file="$output_dir/ssl_certificates_${TIMESTAMP}.json"
    local csv_file="$output_dir/ssl_certificates_${TIMESTAMP}.csv"

    # Get SSL certificates in JSON format
    if ! gcloud compute ssl-certificates list --project="$project_id" --format=json > "$json_file" 2>/dev/null; then
        print_warning "Failed to retrieve SSL certificates. API might not be enabled or no permissions."
        return 1
    fi

    local count=$(jq length "$json_file")
    print_info "Found $count SSL certificates"

    if [ "$count" -eq 0 ]; then
        print_warning "No SSL certificates found in project $project_id"
        return 0
    fi

    # Convert to CSV format
    if [ "$format" = "csv" ] || [ "$format" = "table" ]; then
        echo "ProjectID,Name,Type,Description,CreationTimestamp,ExpireTime,SubjectAlternativeNames,Managed" > "$csv_file"

        jq -r --arg project_id "$project_id" '
        .[] |
        [
            $project_id,
            .name // "",
            .type // "",
            (.description // "" | gsub(","; " ; ")),
            .creationTimestamp // "",
            .expireTime // "",
            (if .subjectAlternativeNames then (.subjectAlternativeNames | join(" ; ")) else "" end),
            (if .managed then "true" else "false" end)
        ] | @csv
        ' "$json_file" >> "$csv_file"

        print_success "SSL certificates exported to: $csv_file"
    fi

    if [ "$format" = "json" ]; then
        print_success "SSL certificates exported to: $json_file"
    fi
}

# Function to extract URL maps
extract_url_maps() {
    local project_id=$1
    local output_dir=$2
    local format=$3

    print_info "Extracting URL Maps for project: $project_id"

    local json_file="$output_dir/url_maps_${TIMESTAMP}.json"
    local csv_file="$output_dir/url_maps_${TIMESTAMP}.csv"

    # Get URL maps in JSON format
    if ! gcloud compute url-maps list --project="$project_id" --format=json > "$json_file" 2>/dev/null; then
        print_warning "Failed to retrieve URL maps. API might not be enabled or no permissions."
        return 1
    fi

    local count=$(jq length "$json_file")
    print_info "Found $count URL maps"

    if [ "$count" -eq 0 ]; then
        print_warning "No URL maps found in project $project_id"
        return 0
    fi

    # Convert to CSV format
    if [ "$format" = "csv" ] || [ "$format" = "table" ]; then
        echo "ProjectID,Name,Description,CreationTimestamp,DefaultService,HostRules,PathMatchers" > "$csv_file"

        jq -r --arg project_id "$project_id" '
        .[] |
        [
            $project_id,
            .name // "",
            (.description // "" | gsub(","; " ; ")),
            .creationTimestamp // "",
            (.defaultService // "" | split("/")[-1]),
            (if .hostRules then (.hostRules | length | tostring) else "0" end),
            (if .pathMatchers then (.pathMatchers | length | tostring) else "0" end)
        ] | @csv
        ' "$json_file" >> "$csv_file"

        print_success "URL maps exported to: $csv_file"
    fi

    if [ "$format" = "json" ]; then
        print_success "URL maps exported to: $json_file"
    fi
}

# Function to extract target pools
extract_target_pools() {
    local project_id=$1
    local output_dir=$2
    local format=$3

    print_info "Extracting Target Pools for project: $project_id"

    local json_file="$output_dir/target_pools_${TIMESTAMP}.json"
    local csv_file="$output_dir/target_pools_${TIMESTAMP}.csv"

    # Get target pools in JSON format
    if ! gcloud compute target-pools list --project="$project_id" --format=json > "$json_file" 2>/dev/null; then
        print_warning "Failed to retrieve target pools. API might not be enabled or no permissions."
        return 1
    fi

    local count=$(jq length "$json_file")
    print_info "Found $count target pools"

    if [ "$count" -eq 0 ]; then
        print_warning "No target pools found in project $project_id"
        return 0
    fi

    # Convert to CSV format
    if [ "$format" = "csv" ] || [ "$format" = "table" ]; then
        echo "ProjectID,Name,Description,CreationTimestamp,Region,SessionAffinity,HealthChecks,Instances" > "$csv_file"

        jq -r --arg project_id "$project_id" '
        .[] |
        [
            $project_id,
            .name // "",
            (.description // "" | gsub(","; " ; ")),
            .creationTimestamp // "",
            (.region // "" | split("/")[-1]),
            .sessionAffinity // "",
            (if .healthChecks then (.healthChecks | map(split("/")[-1]) | join(" ; ")) else "" end),
            (if .instances then (.instances | map(split("/")[-1]) | join(" ; ")) else "" end)
        ] | @csv
        ' "$json_file" >> "$csv_file"

        print_success "Target pools exported to: $csv_file"
    fi

    if [ "$format" = "json" ]; then
        print_success "Target pools exported to: $json_file"
    fi
}

# Function to generate summary report
generate_summary() {
    local project_id=$1
    local output_dir=$2

    print_info "Generating Load Balancer Summary Report"

    local summary_file="$output_dir/lb_summary_${TIMESTAMP}.txt"

    cat > "$summary_file" << EOF
GCP Load Balancer Inventory Summary
===================================
Project: $project_id
Generated: $(date)
Output Directory: $output_dir

Component Summary:
EOF

    # Count components from JSON files
    for component in forwarding_rules backend_services backend_buckets health_checks ssl_certificates url_maps target_pools; do
        local json_file="$output_dir/${component}_${TIMESTAMP}.json"
        if [ -f "$json_file" ]; then
            local count=$(jq length "$json_file" 2>/dev/null || echo "0")
            local display_name=$(echo "$component" | sed 's/_/ /g' | sed 's/\b\w/\U&/g')
            echo "- $display_name: $count" >> "$summary_file"
        fi
    done

    cat >> "$summary_file" << EOF

Files Generated:
$(ls -la "$output_dir"/*_${TIMESTAMP}.* 2>/dev/null | awk '{print "- " $9}' || echo "- No files generated")

Load Balancer Types Found:
EOF

    # Analyze load balancer types from forwarding rules
    local fr_file="$output_dir/forwarding_rules_${TIMESTAMP}.json"
    if [ -f "$fr_file" ]; then
        jq -r '.[] | .loadBalancingScheme // "UNKNOWN"' "$fr_file" | sort | uniq -c | while read count scheme; do
            echo "- $scheme: $count" >> "$summary_file"
        done
    fi

    print_success "Summary report generated: $summary_file"

    # Display summary on screen
    cat "$summary_file"
}

# Function to process multiple projects in parallel
process_multiple_projects() {
    local projects=("$@")
    local total_projects=${#projects[@]}

    print_info "Processing $total_projects projects with max $MAX_PARALLEL_JOBS parallel jobs"
    print_info "Quick scan mode: $QUICK_SCAN"
    echo ""

    local job_count=0
    local completed=0

    for project in "${projects[@]}"; do
        # Wait if we've reached max parallel jobs
        while [ "$job_count" -ge "$MAX_PARALLEL_JOBS" ]; do
            wait -n  # Wait for any job to complete
            job_count=$((job_count - 1))
            completed=$((completed + 1))
            print_info "Progress: $completed/$total_projects projects completed"
        done

        # Start new job in background
        {
            extract_project_lb_data "$project" "$OUTPUT_DIR" "$OUTPUT_FORMAT" "$QUICK_SCAN"
        } &

        job_count=$((job_count + 1))
    done

    # Wait for all remaining jobs to complete
    wait
    completed=$total_projects
    print_success "All $completed projects processed!"
}

# Function to generate consolidated summary across all projects
generate_consolidated_summary() {
    local output_dir=$1
    local summary_file="$output_dir/consolidated_summary_${TIMESTAMP}.txt"

    print_info "Generating consolidated summary..."

    cat > "$summary_file" << EOF
GCP Load Balancer Consolidated Summary
=====================================
Generated: $(date)
Scan Type: $([ "$QUICK_SCAN" = "true" ] && echo "Quick Scan" || echo "Full Scan")
Output Directory: $output_dir

EOF

    local total_projects=0
    local projects_with_lb=0
    local total_lbs=0

    # Process each project directory
    for project_dir in "$output_dir"/*/; do
        if [ -d "$project_dir" ]; then
            local project_name=$(basename "$project_dir")
            local project_summary="$project_dir/summary.txt"

            if [ -f "$project_summary" ]; then
                total_projects=$((total_projects + 1))

                # Extract LB count from project summary
                local lb_count=$(grep "Total Load Balancers:" "$project_summary" | awk '{print $4}' || echo "0")

                if [ "$lb_count" -gt 0 ]; then
                    projects_with_lb=$((projects_with_lb + 1))
                    total_lbs=$((total_lbs + lb_count))
                    echo "$project_name: $lb_count load balancers" >> "$summary_file"
                fi
            fi
        fi
    done

    # Add summary statistics
    cat >> "$summary_file" << EOF

Summary Statistics:
==================
Total Projects Scanned: $total_projects
Projects with Load Balancers: $projects_with_lb
Projects without Load Balancers: $((total_projects - projects_with_lb))
Total Load Balancers Found: $total_lbs

EOF

    # Add load balancer type distribution
    echo "Load Balancer Type Distribution:" >> "$summary_file"
    echo "================================" >> "$summary_file"

    # Aggregate all forwarding rules data
    local temp_file="/tmp/all_forwarding_rules_$$.json"
    echo "[]" > "$temp_file"

    for project_dir in "$output_dir"/*/; do
        if [ -d "$project_dir" ]; then
            local fr_file="$project_dir/forwarding_rules_${TIMESTAMP}.json"
            if [ -f "$fr_file" ]; then
                jq -s '.[0] + .[1]' "$temp_file" "$fr_file" > "${temp_file}.tmp" && mv "${temp_file}.tmp" "$temp_file"
            fi
        fi
    done

    # Generate type distribution
    if [ -f "$temp_file" ]; then
        jq -r '.[] | .loadBalancingScheme // "UNKNOWN"' "$temp_file" | sort | uniq -c | while read count scheme; do
            echo "- $scheme: $count" >> "$summary_file"
        done
    fi

    rm -f "$temp_file" "${temp_file}.tmp"

    print_success "Consolidated summary generated: $summary_file"

    # Display summary on screen
    echo ""
    echo "=== CONSOLIDATED SUMMARY ==="
    cat "$summary_file"
}

# Main extraction function (updated for single project)
extract_all_lb_components() {
    local project_id=$1
    local output_dir=$2
    local format=$3

    print_info "Starting comprehensive Load Balancer extraction for project: $project_id"
    print_info "Output format: $format"
    print_info "Output directory: $output_dir"
    echo ""

    # Extract all components
    extract_forwarding_rules "$project_id" "$output_dir" "$format"
    echo ""
    extract_backend_services "$project_id" "$output_dir" "$format"
    echo ""
    extract_backend_buckets "$project_id" "$output_dir" "$format"
    echo ""
    extract_health_checks "$project_id" "$output_dir" "$format"
    echo ""
    extract_ssl_certificates "$project_id" "$output_dir" "$format"
    echo ""
    extract_url_maps "$project_id" "$output_dir" "$format"
    echo ""
    extract_target_pools "$project_id" "$output_dir" "$format"
    echo ""

    # Generate summary
    generate_summary "$project_id" "$output_dir"

    print_success "Load Balancer extraction completed!"
    print_info "All files saved to: $output_dir"
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -p|--project)
                PROJECT_ID="$2"
                shift 2
                ;;
            -a|--all-projects)
                ALL_PROJECTS=true
                shift
                ;;
            -o|--output-dir)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            -f|--format)
                OUTPUT_FORMAT="$2"
                shift 2
                ;;
            -j|--max-jobs)
                MAX_PARALLEL_JOBS="$2"
                shift 2
                ;;
            -q|--quick-scan)
                QUICK_SCAN=true
                shift
                ;;
            --no-filter)
                FILTER_PROJECTS_WITH_LB=false
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    # Validate required arguments
    if [ -z "$PROJECT_ID" ] && [ "$ALL_PROJECTS" = "false" ]; then
        print_error "Either project ID (-p) or --all-projects (-a) is required."
        show_usage
        exit 1
    fi

    if [ -n "$PROJECT_ID" ] && [ "$ALL_PROJECTS" = "true" ]; then
        print_error "Cannot specify both project ID and --all-projects. Choose one."
        show_usage
        exit 1
    fi

    # Validate format
    if [[ ! "$OUTPUT_FORMAT" =~ ^(csv|json|table)$ ]]; then
        print_error "Invalid format: $OUTPUT_FORMAT. Must be csv, json, or table."
        exit 1
    fi

    # Validate max parallel jobs
    if ! [[ "$MAX_PARALLEL_JOBS" =~ ^[0-9]+$ ]] || [ "$MAX_PARALLEL_JOBS" -lt 1 ] || [ "$MAX_PARALLEL_JOBS" -gt 20 ]; then
        print_error "Invalid max-jobs: $MAX_PARALLEL_JOBS. Must be a number between 1 and 20."
        exit 1
    fi
}

# Main execution
main() {
    echo "GCP Load Balancer Information Extractor"
    echo "========================================"
    echo ""

    # Parse arguments
    parse_arguments "$@"

    # Check prerequisites
    check_prerequisites

    # Validate project
    validate_project "$PROJECT_ID"

    # Setup output directory
    setup_output_dir "$OUTPUT_DIR"

    # Extract all load balancer components
    extract_all_lb_components "$PROJECT_ID" "$OUTPUT_DIR" "$OUTPUT_FORMAT"
}

# Run main function with all arguments
main "$@"
