#!/bin/bash

# GCP Load Balancer Information Extractor
# This script extracts comprehensive load balancer information from GCP projects
# Converted from PowerShell functions.ps1 to bash for direct use in GCP Cloud Shell

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
PROJECT_ID=""
OUTPUT_DIR="./lb_output"
OUTPUT_FORMAT="csv"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Extract comprehensive GCP Load Balancer information including:
- Forwarding Rules (Frontend)
- Backend Services
- Backend Buckets
- Health Checks
- SSL Certificates
- URL Maps
- Target Pools

OPTIONS:
    -p, --project PROJECT_ID    GCP Project ID (required)
    -o, --output-dir DIR        Output directory (default: ./lb_output)
    -f, --format FORMAT         Output format: csv, json, table (default: csv)
    -h, --help                  Show this help message

EXAMPLES:
    $0 -p my-gcp-project
    $0 -p my-project -o /tmp/lb_data -f json
    $0 --project my-project --output-dir ./reports --format table

REQUIREMENTS:
    - gcloud CLI installed and authenticated
    - jq installed (for JSON processing)
    - Appropriate GCP permissions (Compute Network Viewer role)

EOF
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        print_error "jq is not installed. Please install it first."
        print_info "Install with: sudo apt-get install jq (Ubuntu/Debian) or brew install jq (macOS)"
        exit 1
    fi
    
    # Check if authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
        print_error "Not authenticated with gcloud. Please run 'gcloud auth login' first."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to validate project access
validate_project() {
    local project_id=$1
    print_info "Validating access to project: $project_id"
    
    if ! gcloud projects describe "$project_id" &> /dev/null; then
        print_error "Cannot access project '$project_id'. Please check project ID and permissions."
        exit 1
    fi
    
    print_success "Project access validated"
}

# Function to create output directory
setup_output_dir() {
    local output_dir=$1
    print_info "Setting up output directory: $output_dir"
    
    mkdir -p "$output_dir"
    if [ ! -w "$output_dir" ]; then
        print_error "Cannot write to output directory: $output_dir"
        exit 1
    fi
    
    print_success "Output directory ready"
}

# Function to extract forwarding rules (Load Balancer Frontend)
extract_forwarding_rules() {
    local project_id=$1
    local output_dir=$2
    local format=$3
    
    print_info "Extracting Forwarding Rules (LB Frontend) for project: $project_id"
    
    local json_file="$output_dir/forwarding_rules_${TIMESTAMP}.json"
    local csv_file="$output_dir/lb_frontend_${TIMESTAMP}.csv"
    
    # Get forwarding rules in JSON format
    if ! gcloud compute forwarding-rules list --project="$project_id" --format=json > "$json_file" 2>/dev/null; then
        print_warning "Failed to retrieve forwarding rules. API might not be enabled or no permissions."
        return 1
    fi
    
    local count=$(jq length "$json_file")
    print_info "Found $count forwarding rules"
    
    if [ "$count" -eq 0 ]; then
        print_warning "No forwarding rules found in project $project_id"
        return 0
    fi
    
    # Convert to CSV format
    if [ "$format" = "csv" ] || [ "$format" = "table" ]; then
        echo "ProjectID,Name,Target,Description,LoadBalancingScheme,Location,Network,Subnetwork,IPProtocol,IPAddress,Ports,PortRange,BackendService,CreationTimestamp,Labels" > "$csv_file"
        
        jq -r --arg project_id "$project_id" '
        .[] | 
        [
            $project_id,
            .name // "",
            (.target // "" | split("/")[-1]),
            (.description // "" | gsub(","; " ; ")),
            .loadBalancingScheme // "",
            (if .region then (.region | split("/")[-1]) else (.target // "" | split("/")[-3]) end),
            (.network // "" | split("/")[-1]),
            (.subnetwork // "" | split("/")[-1]),
            .IPProtocol // "",
            .IPAddress // "",
            (if .ports then (.ports | join(" ; ")) else "" end),
            .portRange // "",
            (.backendService // "" | split("/")[-1]),
            .creationTimestamp // "",
            (if .labels then (.labels | to_entries | map("\(.key):\(.value)") | join(" ; ")) else "" end)
        ] | @csv
        ' "$json_file" >> "$csv_file"
        
        print_success "Forwarding rules exported to: $csv_file"
    fi
    
    if [ "$format" = "json" ]; then
        print_success "Forwarding rules exported to: $json_file"
    fi
    
    if [ "$format" = "table" ]; then
        print_info "Forwarding Rules Summary:"
        echo "----------------------------------------"
        jq -r --arg project_id "$project_id" '
        .[] | 
        "Name: \(.name // "N/A")
Type: \(.loadBalancingScheme // "N/A") 
Location: \(if .region then (.region | split("/")[-1]) else (.target // "" | split("/")[-3]) end)
IP: \(.IPAddress // "N/A")
Target: \(.target // "" | split("/")[-1])
Backend: \(.backendService // "" | split("/")[-1])
----------------------------------------"
        ' "$json_file"
    fi
}

# Function to extract backend services
extract_backend_services() {
    local project_id=$1
    local output_dir=$2
    local format=$3

    print_info "Extracting Backend Services for project: $project_id"

    local json_file="$output_dir/backend_services_${TIMESTAMP}.json"
    local csv_file="$output_dir/lb_backend_${TIMESTAMP}.csv"

    # Get backend services in JSON format
    if ! gcloud compute backend-services list --project="$project_id" --format=json > "$json_file" 2>/dev/null; then
        print_warning "Failed to retrieve backend services. API might not be enabled or no permissions."
        return 1
    fi

    local count=$(jq length "$json_file")
    print_info "Found $count backend services"

    if [ "$count" -eq 0 ]; then
        print_warning "No backend services found in project $project_id"
        return 0
    fi

    # Convert to CSV format
    if [ "$format" = "csv" ] || [ "$format" = "table" ]; then
        echo "ProjectID,Name,Description,CreationTimestamp,LoadBalancingScheme,Location,Port,PortName,Protocol,HealthChecks,BackendGroups,Logging,LogSampleRate,AffinityCookieTtlSec,ConnectionDrainingTime,EnableCDN,SecurityPolicy,SessionAffinity,Timeout" > "$csv_file"

        jq -r --arg project_id "$project_id" '
        .[] |
        [
            $project_id,
            .name // "",
            (.description // "" | gsub(","; " ; ")),
            .creationTimestamp // "",
            .loadBalancingScheme // "",
            (.selfLink | split("/")[-3]),
            .port // "",
            .portName // "",
            .protocol // "",
            (if .healthChecks then (.healthChecks | map(split("/")[-1]) | join(" ; ")) else "" end),
            (if .backends then (.backends | map(.group | split("/")[-1]) | join(" ; ")) else "" end),
            (.logConfig.enable // ""),
            (.logConfig.sampleRate // ""),
            .affinityCookieTtlSec // "",
            (.connectionDraining.drainingTimeoutSec // ""),
            .enableCDN // "",
            (.securityPolicy // "" | split("/")[-1]),
            .sessionAffinity // "",
            .timeoutSec // ""
        ] | @csv
        ' "$json_file" >> "$csv_file"

        print_success "Backend services exported to: $csv_file"
    fi

    if [ "$format" = "json" ]; then
        print_success "Backend services exported to: $json_file"
    fi
}

# Function to extract backend buckets
extract_backend_buckets() {
    local project_id=$1
    local output_dir=$2
    local format=$3

    print_info "Extracting Backend Buckets for project: $project_id"

    local json_file="$output_dir/backend_buckets_${TIMESTAMP}.json"
    local csv_file="$output_dir/lb_backend_buckets_${TIMESTAMP}.csv"

    # Get backend buckets in JSON format
    if ! gcloud compute backend-buckets list --project="$project_id" --format=json > "$json_file" 2>/dev/null; then
        print_warning "Failed to retrieve backend buckets. API might not be enabled or no permissions."
        return 1
    fi

    local count=$(jq length "$json_file")
    print_info "Found $count backend buckets"

    if [ "$count" -eq 0 ]; then
        print_warning "No backend buckets found in project $project_id"
        return 0
    fi

    # Convert to CSV format
    if [ "$format" = "csv" ] || [ "$format" = "table" ]; then
        echo "ProjectID,Name,BucketName,Description,CreationTimestamp,Location,EnableCDN,CacheMode,ClientTtl,DefaultTtl,MaxTtl" > "$csv_file"

        jq -r --arg project_id "$project_id" '
        .[] |
        [
            $project_id,
            .name // "",
            .bucketName // "",
            (.description // "" | gsub(","; " ; ")),
            .creationTimestamp // "",
            (.selfLink | split("/")[-3]),
            .enableCDN // "",
            (.cdnPolicy.cacheMode // ""),
            (.cdnPolicy.clientTtl // ""),
            (.cdnPolicy.defaultTtl // ""),
            (.cdnPolicy.maxTtl // "")
        ] | @csv
        ' "$json_file" >> "$csv_file"

        print_success "Backend buckets exported to: $csv_file"
    fi

    if [ "$format" = "json" ]; then
        print_success "Backend buckets exported to: $json_file"
    fi
}

# Function to extract health checks
extract_health_checks() {
    local project_id=$1
    local output_dir=$2
    local format=$3

    print_info "Extracting Health Checks for project: $project_id"

    local json_file="$output_dir/health_checks_${TIMESTAMP}.json"
    local csv_file="$output_dir/health_checks_${TIMESTAMP}.csv"

    # Get health checks in JSON format
    if ! gcloud compute health-checks list --project="$project_id" --format=json > "$json_file" 2>/dev/null; then
        print_warning "Failed to retrieve health checks. API might not be enabled or no permissions."
        return 1
    fi

    local count=$(jq length "$json_file")
    print_info "Found $count health checks"

    if [ "$count" -eq 0 ]; then
        print_warning "No health checks found in project $project_id"
        return 0
    fi

    # Convert to CSV format
    if [ "$format" = "csv" ] || [ "$format" = "table" ]; then
        echo "ProjectID,Name,Type,Description,CheckIntervalSec,TimeoutSec,HealthyThreshold,UnhealthyThreshold,Port,RequestPath,Host" > "$csv_file"

        jq -r --arg project_id "$project_id" '
        .[] |
        [
            $project_id,
            .name // "",
            .type // "",
            (.description // "" | gsub(","; " ; ")),
            .checkIntervalSec // "",
            .timeoutSec // "",
            .healthyThreshold // "",
            .unhealthyThreshold // "",
            (if .httpHealthCheck then .httpHealthCheck.port elif .httpsHealthCheck then .httpsHealthCheck.port elif .tcpHealthCheck then .tcpHealthCheck.port else "" end),
            (if .httpHealthCheck then .httpHealthCheck.requestPath elif .httpsHealthCheck then .httpsHealthCheck.requestPath else "" end),
            (if .httpHealthCheck then .httpHealthCheck.host elif .httpsHealthCheck then .httpsHealthCheck.host else "" end)
        ] | @csv
        ' "$json_file" >> "$csv_file"

        print_success "Health checks exported to: $csv_file"
    fi

    if [ "$format" = "json" ]; then
        print_success "Health checks exported to: $json_file"
    fi
}

# Function to extract SSL certificates
extract_ssl_certificates() {
    local project_id=$1
    local output_dir=$2
    local format=$3

    print_info "Extracting SSL Certificates for project: $project_id"

    local json_file="$output_dir/ssl_certificates_${TIMESTAMP}.json"
    local csv_file="$output_dir/ssl_certificates_${TIMESTAMP}.csv"

    # Get SSL certificates in JSON format
    if ! gcloud compute ssl-certificates list --project="$project_id" --format=json > "$json_file" 2>/dev/null; then
        print_warning "Failed to retrieve SSL certificates. API might not be enabled or no permissions."
        return 1
    fi

    local count=$(jq length "$json_file")
    print_info "Found $count SSL certificates"

    if [ "$count" -eq 0 ]; then
        print_warning "No SSL certificates found in project $project_id"
        return 0
    fi

    # Convert to CSV format
    if [ "$format" = "csv" ] || [ "$format" = "table" ]; then
        echo "ProjectID,Name,Type,Description,CreationTimestamp,ExpireTime,SubjectAlternativeNames,Managed" > "$csv_file"

        jq -r --arg project_id "$project_id" '
        .[] |
        [
            $project_id,
            .name // "",
            .type // "",
            (.description // "" | gsub(","; " ; ")),
            .creationTimestamp // "",
            .expireTime // "",
            (if .subjectAlternativeNames then (.subjectAlternativeNames | join(" ; ")) else "" end),
            (if .managed then "true" else "false" end)
        ] | @csv
        ' "$json_file" >> "$csv_file"

        print_success "SSL certificates exported to: $csv_file"
    fi

    if [ "$format" = "json" ]; then
        print_success "SSL certificates exported to: $json_file"
    fi
}

# Function to extract URL maps
extract_url_maps() {
    local project_id=$1
    local output_dir=$2
    local format=$3

    print_info "Extracting URL Maps for project: $project_id"

    local json_file="$output_dir/url_maps_${TIMESTAMP}.json"
    local csv_file="$output_dir/url_maps_${TIMESTAMP}.csv"

    # Get URL maps in JSON format
    if ! gcloud compute url-maps list --project="$project_id" --format=json > "$json_file" 2>/dev/null; then
        print_warning "Failed to retrieve URL maps. API might not be enabled or no permissions."
        return 1
    fi

    local count=$(jq length "$json_file")
    print_info "Found $count URL maps"

    if [ "$count" -eq 0 ]; then
        print_warning "No URL maps found in project $project_id"
        return 0
    fi

    # Convert to CSV format
    if [ "$format" = "csv" ] || [ "$format" = "table" ]; then
        echo "ProjectID,Name,Description,CreationTimestamp,DefaultService,HostRules,PathMatchers" > "$csv_file"

        jq -r --arg project_id "$project_id" '
        .[] |
        [
            $project_id,
            .name // "",
            (.description // "" | gsub(","; " ; ")),
            .creationTimestamp // "",
            (.defaultService // "" | split("/")[-1]),
            (if .hostRules then (.hostRules | length | tostring) else "0" end),
            (if .pathMatchers then (.pathMatchers | length | tostring) else "0" end)
        ] | @csv
        ' "$json_file" >> "$csv_file"

        print_success "URL maps exported to: $csv_file"
    fi

    if [ "$format" = "json" ]; then
        print_success "URL maps exported to: $json_file"
    fi
}

# Function to extract target pools
extract_target_pools() {
    local project_id=$1
    local output_dir=$2
    local format=$3

    print_info "Extracting Target Pools for project: $project_id"

    local json_file="$output_dir/target_pools_${TIMESTAMP}.json"
    local csv_file="$output_dir/target_pools_${TIMESTAMP}.csv"

    # Get target pools in JSON format
    if ! gcloud compute target-pools list --project="$project_id" --format=json > "$json_file" 2>/dev/null; then
        print_warning "Failed to retrieve target pools. API might not be enabled or no permissions."
        return 1
    fi

    local count=$(jq length "$json_file")
    print_info "Found $count target pools"

    if [ "$count" -eq 0 ]; then
        print_warning "No target pools found in project $project_id"
        return 0
    fi

    # Convert to CSV format
    if [ "$format" = "csv" ] || [ "$format" = "table" ]; then
        echo "ProjectID,Name,Description,CreationTimestamp,Region,SessionAffinity,HealthChecks,Instances" > "$csv_file"

        jq -r --arg project_id "$project_id" '
        .[] |
        [
            $project_id,
            .name // "",
            (.description // "" | gsub(","; " ; ")),
            .creationTimestamp // "",
            (.region // "" | split("/")[-1]),
            .sessionAffinity // "",
            (if .healthChecks then (.healthChecks | map(split("/")[-1]) | join(" ; ")) else "" end),
            (if .instances then (.instances | map(split("/")[-1]) | join(" ; ")) else "" end)
        ] | @csv
        ' "$json_file" >> "$csv_file"

        print_success "Target pools exported to: $csv_file"
    fi

    if [ "$format" = "json" ]; then
        print_success "Target pools exported to: $json_file"
    fi
}

# Function to generate summary report
generate_summary() {
    local project_id=$1
    local output_dir=$2

    print_info "Generating Load Balancer Summary Report"

    local summary_file="$output_dir/lb_summary_${TIMESTAMP}.txt"

    cat > "$summary_file" << EOF
GCP Load Balancer Inventory Summary
===================================
Project: $project_id
Generated: $(date)
Output Directory: $output_dir

Component Summary:
EOF

    # Count components from JSON files
    for component in forwarding_rules backend_services backend_buckets health_checks ssl_certificates url_maps target_pools; do
        local json_file="$output_dir/${component}_${TIMESTAMP}.json"
        if [ -f "$json_file" ]; then
            local count=$(jq length "$json_file" 2>/dev/null || echo "0")
            local display_name=$(echo "$component" | sed 's/_/ /g' | sed 's/\b\w/\U&/g')
            echo "- $display_name: $count" >> "$summary_file"
        fi
    done

    cat >> "$summary_file" << EOF

Files Generated:
$(ls -la "$output_dir"/*_${TIMESTAMP}.* 2>/dev/null | awk '{print "- " $9}' || echo "- No files generated")

Load Balancer Types Found:
EOF

    # Analyze load balancer types from forwarding rules
    local fr_file="$output_dir/forwarding_rules_${TIMESTAMP}.json"
    if [ -f "$fr_file" ]; then
        jq -r '.[] | .loadBalancingScheme // "UNKNOWN"' "$fr_file" | sort | uniq -c | while read count scheme; do
            echo "- $scheme: $count" >> "$summary_file"
        done
    fi

    print_success "Summary report generated: $summary_file"

    # Display summary on screen
    cat "$summary_file"
}

# Main extraction function
extract_all_lb_components() {
    local project_id=$1
    local output_dir=$2
    local format=$3

    print_info "Starting comprehensive Load Balancer extraction for project: $project_id"
    print_info "Output format: $format"
    print_info "Output directory: $output_dir"
    echo ""

    # Extract all components
    extract_forwarding_rules "$project_id" "$output_dir" "$format"
    echo ""
    extract_backend_services "$project_id" "$output_dir" "$format"
    echo ""
    extract_backend_buckets "$project_id" "$output_dir" "$format"
    echo ""
    extract_health_checks "$project_id" "$output_dir" "$format"
    echo ""
    extract_ssl_certificates "$project_id" "$output_dir" "$format"
    echo ""
    extract_url_maps "$project_id" "$output_dir" "$format"
    echo ""
    extract_target_pools "$project_id" "$output_dir" "$format"
    echo ""

    # Generate summary
    generate_summary "$project_id" "$output_dir"

    print_success "Load Balancer extraction completed!"
    print_info "All files saved to: $output_dir"
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -p|--project)
                PROJECT_ID="$2"
                shift 2
                ;;
            -o|--output-dir)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            -f|--format)
                OUTPUT_FORMAT="$2"
                shift 2
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    # Validate required arguments
    if [ -z "$PROJECT_ID" ]; then
        print_error "Project ID is required. Use -p or --project option."
        show_usage
        exit 1
    fi

    # Validate format
    if [[ ! "$OUTPUT_FORMAT" =~ ^(csv|json|table)$ ]]; then
        print_error "Invalid format: $OUTPUT_FORMAT. Must be csv, json, or table."
        exit 1
    fi
}

# Main execution
main() {
    echo "GCP Load Balancer Information Extractor"
    echo "========================================"
    echo ""

    # Parse arguments
    parse_arguments "$@"

    # Check prerequisites
    check_prerequisites

    # Validate project
    validate_project "$PROJECT_ID"

    # Setup output directory
    setup_output_dir "$OUTPUT_DIR"

    # Extract all load balancer components
    extract_all_lb_components "$PROJECT_ID" "$OUTPUT_DIR" "$OUTPUT_FORMAT"
}

# Run main function with all arguments
main "$@"
