import os
import zipfile
import subprocess
import uuid

def update_codmod_report(report_path, target_cloud):
    if target_cloud.lower() != "aws" or not os.path.isfile(report_path):
        return report_path
    support = """<a href="#" id="feedback-button" class="toolbar-button">
                <span class="material-icons">feedback</span>
                <span>Feedback</span>
            </a>
            <a href="#" id="support-button" class="toolbar-button">
                <span class="material-icons">support_agent</span>
                <span>Support</span>
            </a>"""
    command = """<div class="collapsible-container">
                                    <h2 class="collapsible-header">Command Line Options</h2>"""
    replace_command = """<div class="collapsible-container" style="display: none;">
                                    <h2 class="collapsible-header">Command Line Options</h2>"""
    
    with open(report_path, 'r') as file:
        content = file.read()

    content = content.replace("Google Cloud Code Modernization Assessment Tool", "Hypatia Code Modernization Assessment Tool")
    content = content.replace("Cloud Resources & Google Cloud Mapping", f"Cloud Resources & {target_cloud} Mapping")
    content = content.replace("Google Cloud Architecture", f"{target_cloud} Architecture")
    content = content.replace("&copy; 2025 Google Cloud", "&copy; 2025 Hypatia")
    content = content.replace("Google Cloud - Code Assessment | Cloud to Cloud", "Hypatia - Code Assessment | Cloud to Cloud")
    content = content.replace(support, "")
    content = content.replace(command, replace_command)

    with open(report_path, 'w') as file:
        file.write(content)

    return report_path


def ApplicationAssessment(request, rootpath):
    zip_file = request.files.get('zip_file')
    target_cloud = request.form.get('target_cloud')

    if not zip_file or not target_cloud:
        return "", "Missing file or target cloud", "Error: Missing file or target cloud", 1

    session_id = str(uuid.uuid4())
    upload_dir = os.path.join(rootpath, 'output', session_id)
    os.makedirs(upload_dir, exist_ok=True)

    zip_path = os.path.join(upload_dir, zip_file.filename)
    zip_file.save(zip_path)

    unzip_dir = os.path.join(upload_dir, 'source')
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(unzip_dir)

    report_path = os.path.join(upload_dir, 'report.html')

    # Find the root directory of the unzipped application
    unzipped_files = os.listdir(unzip_dir)
    if len(unzipped_files) == 1 and os.path.isdir(os.path.join(unzip_dir, unzipped_files[0])):
        app_root = os.path.join(unzip_dir, unzipped_files[0])
    else:
        app_root = unzip_dir
    total_cost = 0.0

    codmod_cost_command = [
            'codmod', 'create',
            '-c', app_root,
            '-o', report_path,
            '--intent', 'CLOUD_TO_CLOUD',
            '--modelset', '2.5-flash',
            '--estimate-cost'
        ]

    report = subprocess.Popen(codmod_cost_command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)

    for line in report.stdout:
        if "Estimated total cost" in line:
            total_cost = float(line.strip().split(":")[1].strip().split(" ")[0])
            # print(float(line.strip().split(":")[1].strip().split(" ")[0]))
            break

    codmod_command = [
        'codmod', 'create',
        '-c', app_root,
        '-o', report_path,
        '--intent', 'CLOUD_TO_CLOUD',
        '--modelset', '2.5-flash',
        '--experiments=enable_images',
        '--context', f"intent: CLOUD_TO_CLOUD, target_cloud: {target_cloud}"
    ]

    try:
        # Since the command is executed from the rootpath, adjust the path to codmod if necessary
        # codmod_executable = os.path.join(rootpath, 'codmod')
        # if not os.path.exists(codmod_executable):
        #      return "", "codmod executable not found", "Error: 'codmod' command not found in the root directory.", 1

        command_to_run = codmod_command
        
        # Change to rootpath to execute the command
        os.chdir(rootpath)
        result = subprocess.run(command_to_run, check=True, capture_output=True, text=True)
        
        if os.path.isfile(report_path):
            try:
                report_path = update_codmod_report(report_path, target_cloud)
            except Exception as e:
                pass
            msg = "Application assessment report generated successfully."
            return report_path, total_cost, msg, 0
        else:
            err = "Report file not generated."
            return "", err, err, 1
            
    except subprocess.CalledProcessError as e:
        return "", f"Error running codmod: {e.stderr}", f"Error running codmod: {e.stderr}", 1
    finally:
        # Always change back to the original directory
        os.chdir(rootpath)
